diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itempricebottomtags/common/strategy/promo/MagicalMemberPromoTagBuildStrategy.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itempricebottomtags/common/strategy/promo/MagicalMemberPromoTagBuildStrategy.java
index 12c11e1cc2..d634c1d74e 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itempricebottomtags/common/strategy/promo/MagicalMemberPromoTagBuildStrategy.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itempricebottomtags/common/strategy/promo/MagicalMemberPromoTagBuildStrategy.java
@@ -105,8 +105,11 @@ public class MagicalMemberPromoTagBuildStrategy extends AbstractPriceBottomPromo
             resetNormalStyle(dzTagVO, afterInflate || (canInflate && hasInflateInfo));
             return;
         }
+        // 神券还可膨胀，但膨胀后能不能形成更优算价组合
+        Map<String, String> extendDisplayInfo = DzPromoUtils.getExtendDisplayInfo(promoPriceM);
+        boolean canInflateAndCanInflateMore = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(req.getContext(),extendDisplayInfo);
         //可膨胀
-        if (canInflate && hasInflateInfo) {
+        if (canInflate && hasInflateInfo && canInflateAndCanInflateMore) {
             List<String> inflateText = MagicalMemberTagUtils.getInflateText(req.getContext(), magicalItemM);
             if (CollectionUtils.isNotEmpty(inflateText)) {
                 dzTagVO.setMagicalTagStyle(buildCanInflateStyle(inflateText));
