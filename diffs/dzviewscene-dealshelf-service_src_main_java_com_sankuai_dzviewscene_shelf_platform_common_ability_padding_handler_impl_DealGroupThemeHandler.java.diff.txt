diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/common/ability/padding/handler/impl/DealGroupThemeHandler.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/common/ability/padding/handler/impl/DealGroupThemeHandler.java
index 9d09e6fdd3..18a5deaa09 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/common/ability/padding/handler/impl/DealGroupThemeHandler.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/common/ability/padding/handler/impl/DealGroupThemeHandler.java
@@ -31,10 +31,7 @@ import com.sankuai.dzviewscene.product.constants.PmfConstants;
 import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
 import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
 import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
-import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
-import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberMonitorUtils;
-import com.sankuai.dzviewscene.product.shelf.utils.PaddingParallelUtils;
-import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
+import com.sankuai.dzviewscene.product.shelf.utils.*;
 import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
 import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
 import com.sankuai.dzviewscene.product.utils.LionObjectManagerUtils;
@@ -51,6 +48,7 @@ import com.sankuai.dzviewscene.shelf.platform.common.model.*;
 import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
 import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
 import com.sankuai.dzviewscene.shelf.platform.utils.*;
+import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
 import com.sankuai.it.iam.common_base.utils.IntegerUtil;
 import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
 import lombok.extern.slf4j.Slf4j;
@@ -1291,10 +1289,17 @@ public class DealGroupThemeHandler implements GroupPaddingHandler {
     }
 
     private String getPromoDetailTemplate(ActivityContext activityContext) {
-        if(PromoSimplifyUtils.hitPromoSimplifyV2(activityContext)){
-            return PromoDetailTemplateEnum.NewVersion.getCode();
+        String promoDetailTemplate = PromoDetailTemplateEnum.OldVersion.getCode();
+        if (PromoSimplifyUtils.hitPromoSimplifyV2(activityContext)) {
+            promoDetailTemplate = PromoDetailTemplateEnum.NewVersion.getCode();
         }
-        return PromoDetailTemplateEnum.OldVersion.getCode();
+        if (MagicalMemberTagUtils.hitExp(activityContext)) {
+            return new StringBuilder()
+                    .append(promoDetailTemplate)
+                    .append(",")
+                    .append(PromoDetailTemplateEnum.EnhancePerception.getCode()).toString();
+        }
+        return promoDetailTemplate;
     }
 
     private String getActivityQueryScene(ActivityContext activityContext) {
