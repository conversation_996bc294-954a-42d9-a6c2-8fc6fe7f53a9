diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOpt.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOpt.java
index 56d658ee6e..5ba32b2378 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOpt.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOpt.java
@@ -1,21 +1,37 @@
 package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceprefix;
 
+import com.sankuai.athena.inf.config.ConfigValue;
 import com.sankuai.athena.viewscene.framework.ActivityCxt;
 import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
 import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
 import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePricePrefixVP;
+import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
+import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
+import com.sankuai.dzviewscene.product.utils.PriceUtils;
+import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
+import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
+import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
+import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
+import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
+import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
 import lombok.Data;
 
 @VPointOption(name = "默认-空变化点",
         description = "默认返回null",
         code = DefaultSalePricePrefixOpt.CODE,
         isDefault = true)
-public class DefaultSalePricePrefixOpt extends ItemSalePricePrefixVP<Void> {
+public class DefaultSalePricePrefixOpt extends ItemSalePricePrefixVP<DefaultSalePricePrefixOpt.Config> {
 
     public static final String CODE = "DefaultSalePricePrefixOpt";
 
+
     @Override
-    public String compute(ActivityCxt context, Param param,Void config) {
-        return null;
+    public String compute(ActivityCxt context, Param param, Config config) {
+        return getPricePrefix(context, param);
+    }
+
+    @VPointCfg
+    @Data
+    public static class Config {
     }
 }
\ No newline at end of file
