diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/shelf/ocean/ShelfCommonOcean.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/shelf/ocean/ShelfCommonOcean.java
index 5d957c6a9f..3b19294882 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/shelf/ocean/ShelfCommonOcean.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/shelf/platform/shelf/ocean/ShelfCommonOcean.java
@@ -45,6 +45,8 @@ import static com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils.MAG
  */
 public class ShelfCommonOcean {
 
+    public static final String PRICE_PREFIX = "神券价";
+
     /**
      * 填充公共打点信息
      *
@@ -358,6 +360,7 @@ public class ShelfCommonOcean {
             labsOceanMap.put(OceanConstantUtils.INDEX, currentIndex);
             labsOceanMap.put(OceanConstantUtils.PRICE, getPrice(productItem));
             labsOceanMap.put(OceanConstantUtils.DZ_REAL_QUERY_ID, flowId);
+            labsOceanMap.put(OceanConstantUtils.PRICE_TITLE, getPriceTitle(productItem));
             // 先用后付标签副标题上报
             labsOceanMap.put(OceanConstantUtils.LABEL_NAME, getLabelName(labsOceanMap, productItem));
             //到综搜推核心展位实时特征打点
@@ -378,6 +381,14 @@ public class ShelfCommonOcean {
         });
     }
 
+    private static Object getPriceTitle(DzItemVO itemVO) {
+        String pricePrefix = itemVO.getSalePricePrefixDesc();
+        if (StringUtils.isNotEmpty(pricePrefix) && PRICE_PREFIX.equals(pricePrefix)) {
+            return pricePrefix;
+        }
+        return StringUtils.EMPTY;
+    }
+
     private static String getLabelName(Map<String, Object> labsOceanMap,DzItemVO productItem) {
         Object labelNameObj = labsOceanMap.get(OceanConstantUtils.LABEL_NAME);
         String labelName = labelNameObj != null ? String.valueOf(labelNameObj) : "";
