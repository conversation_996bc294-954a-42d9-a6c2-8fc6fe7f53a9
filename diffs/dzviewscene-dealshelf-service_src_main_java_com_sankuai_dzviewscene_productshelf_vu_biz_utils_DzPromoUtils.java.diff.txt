diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/productshelf/vu/biz/utils/DzPromoUtils.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/productshelf/vu/biz/utils/DzPromoUtils.java
index db7cc77f28..80e517173d 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/productshelf/vu/biz/utils/DzPromoUtils.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/productshelf/vu/biz/utils/DzPromoUtils.java
@@ -4,6 +4,7 @@ import com.dianping.cat.Cat;
 import com.dianping.cat.util.StringUtils;
 import com.dianping.vc.enums.VCClientTypeEnum;
 import com.google.common.collect.Lists;
+import com.google.common.collect.Maps;
 import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
 import com.sankuai.dztheme.deal.dto.DealProductPromoDTO;
 import com.sankuai.dztheme.deal.dto.DealPromoItemDTO;
@@ -24,6 +25,7 @@ import org.apache.commons.lang.math.NumberUtils;
 
 import java.math.BigDecimal;
 import java.util.List;
+import java.util.Map;
 import java.util.stream.Collectors;
 
 /**
@@ -128,6 +130,13 @@ public class DzPromoUtils {
         return productPromoPriceM.getExtendDisplayInfo().get(EXT_DISPLAY_PROMO_TAG_KEY);
     }
 
+    public static Map<String, String> getExtendDisplayInfo(ProductPromoPriceM productPromoPriceM) {
+        if (MapUtils.isEmpty(productPromoPriceM.getExtendDisplayInfo())) {
+            return Maps.newHashMap();
+        }
+        return productPromoPriceM.getExtendDisplayInfo();
+    }
+
     public static DzPromoDetailVO buildPromoDetail(ProductPromoPriceM productPromoPriceM, int popType) {
         return buildPromoDetail(null, null, productPromoPriceM, popType);
     }
