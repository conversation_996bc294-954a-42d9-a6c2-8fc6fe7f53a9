diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/ocean/utils/UnifiedShelfCommonOcean.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/ocean/utils/UnifiedShelfCommonOcean.java
index d5177fb728..00984ea598 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/ocean/utils/UnifiedShelfCommonOcean.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/ocean/utils/UnifiedShelfCommonOcean.java
@@ -46,6 +46,8 @@ public class UnifiedShelfCommonOcean {
 
     public static final String payLaterTag = "先用后付";
 
+    public static final String PRICE_PREFIX = "神券价";
+
     /**
      * 填充公共打点信息
      */
@@ -463,6 +465,7 @@ public class UnifiedShelfCommonOcean {
         labsOceanMap.put(OceanConstantUtils.STATUS, getStatus(productAreaComponentVO.getDefaultShowNum(), currentIndex, filterBtnId, filterBtnIdsSelected, ctx, hasMultiFilterIdAndProductAreas));
         labsOceanMap.put(OceanConstantUtils.INDEX, currentIndex);
         labsOceanMap.put(OceanConstantUtils.PRICE, getPrice(productItem));
+        labsOceanMap.put(OceanConstantUtils.PRICE_TITLE, getPriceTitle(productItem));
         // 先用后付标签上报
         labsOceanMap.put(OceanConstantUtils.LABEL_NAME, getLabelName(labsOceanMap, productItem));
         //到综搜推核心展位实时特征打点
@@ -481,6 +484,14 @@ public class UnifiedShelfCommonOcean {
         productItem.setLabs(JsonCodec.encodeWithUTF8(labsOceanMap));
     }
 
+    private static Object getPriceTitle(ShelfItemVO itemVO) {
+        String pricePrefix = itemVO.getSalePricePrefix();
+        if (StringUtils.isNotEmpty(pricePrefix) && PRICE_PREFIX.equals(pricePrefix)) {
+            return pricePrefix;
+        }
+        return StringUtils.EMPTY;
+    }
+
     private static String getLabelName(Map<String, Object> labsOceanMap, ShelfItemVO productItem) {
         Object labelNameObj = labsOceanMap.get(OceanConstantUtils.LABEL_NAME);
         String labelName = labelNameObj != null ? String.valueOf(labelNameObj) : "";
