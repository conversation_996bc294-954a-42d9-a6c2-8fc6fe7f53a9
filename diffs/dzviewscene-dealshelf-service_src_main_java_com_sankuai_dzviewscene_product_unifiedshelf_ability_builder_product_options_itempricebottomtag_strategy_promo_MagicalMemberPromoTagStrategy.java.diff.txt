diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itempricebottomtag/strategy/promo/MagicalMemberPromoTagStrategy.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itempricebottomtag/strategy/promo/MagicalMemberPromoTagStrategy.java
index 88eef48b58..c3f348e0cd 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itempricebottomtag/strategy/promo/MagicalMemberPromoTagStrategy.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itempricebottomtag/strategy/promo/MagicalMemberPromoTagStrategy.java
@@ -89,7 +89,10 @@ public class MagicalMemberPromoTagStrategy extends AbstractPriceBottomPromoTagBu
         if (useNormalMagicalTag(req)) {
             return buildNormalShelfMagicalMemberTag(shelfTagVO, promoPriceM, req, afterInflate || (hasInflateInfo && canInflate));
         }
-        if (hasInflateInfo && canInflate) {
+        // 神券还可膨胀，但膨胀后能不能形成更优算价组合
+        Map<String, String> extendDisplayInfo = DzPromoUtils.getExtendDisplayInfo(promoPriceM);
+        boolean canInflateAndCanInflateMore = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(req.getContext(),extendDisplayInfo);
+        if (hasInflateInfo && canInflate && canInflateAndCanInflateMore) {
             //可膨胀
             List<String> inflateText = MagicalMemberTagUtils.getInflateText(req.getContext(), magicalItemM);
             if (CollectionUtils.isNotEmpty(inflateText)) {
