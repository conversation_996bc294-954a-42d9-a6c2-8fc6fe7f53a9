diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/ability/fetcher/douhu/ShelfDouHuFetcher.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/ability/fetcher/douhu/ShelfDouHuFetcher.java
index d1fe0b55d1..4bc259432b 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/ability/fetcher/douhu/ShelfDouHuFetcher.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/ability/fetcher/douhu/ShelfDouHuFetcher.java
@@ -20,6 +20,7 @@ import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivit
 import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
 import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
 import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
+import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
 import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
 import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
 import com.sankuai.dzviewscene.unifiedshelf.operator.api.dto.OperatorShelfConfigDTO;
@@ -184,6 +185,7 @@ public class ShelfDouHuFetcher extends PmfAbility<List<DouHuM>, ShelfDouHuFetche
             return Lists.newArrayList();
         }
         String sceneCode = ctx.getParam(ShelfActivityConstants.Params.sceneCode);
+        ShopM shopM = ctx.getParam(ShelfActivityConstants.Ctx.ctxShop);
         return commonExps.stream().filter(config -> {
             if(config == null || StringUtils.isEmpty(config.getExpId())){
                 return false;
@@ -198,6 +200,24 @@ public class ShelfDouHuFetcher extends PmfAbility<List<DouHuM>, ShelfDouHuFetche
             if(CollectionUtils.isNotEmpty(config.getWhiteScenes()) && config.getWhiteScenes().contains(sceneCode)){
                 return true;
             }
+            if(CollectionUtils.isNotEmpty(config.getBackCategoryIdBlackList())){
+                List<String> backCategoryIdsBlackList = config.getBackCategoryIdBlackList();
+                if(shopM == null){
+                    return true;
+                }
+                //[主] 购物(379)-电子数码(649)//[主] 家居(600)-家用电器(604)
+                List<Integer> backCategory = shopM.getBackCategory();
+                if (CollectionUtils.isNotEmpty(backCategory)) {
+                    String backCategoryStr = backCategory.stream()
+                            .map(String::valueOf)
+                            .collect(Collectors.joining("-"));
+                    for (String backCategoryIdConfig : backCategoryIdsBlackList) {
+                        if (backCategoryStr.equals(backCategoryIdConfig)) {
+                            return false;
+                        }
+                    }
+                }
+            }
             return config.isAllOpen();
         }).map(CommonConfig::getExpId).collect(Collectors.toList());
     }
@@ -305,5 +325,7 @@ public class ShelfDouHuFetcher extends PmfAbility<List<DouHuM>, ShelfDouHuFetche
         private List<String> whiteScenes;
 
         private List<String> blackScenes;
+
+        private List<String> backCategoryIdBlackList;
     }
 }
