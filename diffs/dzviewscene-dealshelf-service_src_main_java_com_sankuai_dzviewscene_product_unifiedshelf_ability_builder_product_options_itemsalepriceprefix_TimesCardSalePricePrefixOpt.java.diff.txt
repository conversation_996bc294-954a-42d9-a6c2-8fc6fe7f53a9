diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/TimesCardSalePricePrefixOpt.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/TimesCardSalePricePrefixOpt.java
index 66dbe2f5ce..f920c96d1e 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/TimesCardSalePricePrefixOpt.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/TimesCardSalePricePrefixOpt.java
@@ -6,6 +6,7 @@ import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
 import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
 import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
 import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
+import org.apache.commons.lang3.StringUtils;
 import org.apache.commons.lang3.math.NumberUtils;
 
 @VPointOption(name = "次卡商品价格前缀",
@@ -30,6 +31,11 @@ public class TimesCardSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<V
         if(param.getProductM().isTimesDeal()){
             return "单次";
         }
+        // 次卡优先级>神券价
+        String pricePrefix = getPricePrefix(context, param);
+        if (StringUtils.isNotBlank(pricePrefix)) {
+            return pricePrefix;
+        }
         return null;
     }
 }
\ No newline at end of file
