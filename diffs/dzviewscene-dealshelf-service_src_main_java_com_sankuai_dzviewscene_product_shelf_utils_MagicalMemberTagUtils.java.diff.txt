diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/utils/MagicalMemberTagUtils.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/utils/MagicalMemberTagUtils.java
index db8ed058ec..b4bc273482 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/utils/MagicalMemberTagUtils.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/utils/MagicalMemberTagUtils.java
@@ -1,24 +1,31 @@
 package com.sankuai.dzviewscene.product.shelf.utils;
 
+import com.dianping.tpfun.product.api.sku.common.utils.GsonUtils;
 import com.dianping.vc.enums.VCClientTypeEnum;
 import com.google.common.collect.Lists;
 import com.sankuai.athena.inf.config.ConfigValue;
 import com.sankuai.athena.viewscene.framework.ActivityCxt;
 import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
+import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
 import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
 import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
 import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
 import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
+import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
 import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
+import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
 import lombok.Data;
 import org.apache.commons.collections4.CollectionUtils;
 import org.apache.commons.collections4.MapUtils;
 import org.apache.commons.lang.math.NumberUtils;
+import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
 
 import java.math.BigDecimal;
 import java.math.RoundingMode;
 import java.util.List;
+import java.util.Map;
+import java.util.Objects;
 
 @Component
 public class MagicalMemberTagUtils {
@@ -71,6 +78,12 @@ public class MagicalMemberTagUtils {
 
     public static String INFLATE_STYLE_B_APPEND = "膨胀";
 
+    //PromotionPropertyEnum.MAGICAL_MEMBER_COUPON_LABEL
+    public static String MAGICAL_MEMBER_COUPON_LABEL = "magicalMemberCouponLabel";
+
+    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.config1", defaultValue = "{}")
+    public static MagicalMemberConfig magicalMemberConfig;
+
     public static List<String> getInflateText(ActivityCxt context, PromoItemM magicalItemM){
         if(magicalMemberStyleConfig == null || magicalItemM == null || MapUtils.isEmpty(magicalItemM.getPromotionOtherInfoMap())){
             return null;
@@ -116,6 +129,93 @@ public class MagicalMemberTagUtils {
         return DouHuUtils.hitAnySk(douHuList, magicalMemberStyleConfig.getExps());
     }
 
+    public static boolean getCanInflateAndCanInflateMore(ActivityCxt context,Map<String, String> extendDisplayInfo) {
+        if (MapUtils.isEmpty(extendDisplayInfo)){
+            return true;
+        }
+        if (Objects.isNull(magicalMemberConfig)) {
+            return true;
+        }
+        // 没有命中客户端保持愿逻辑
+        if (!hitClientType(context)) {
+            return true;
+        }
+        // 命中黑名单城市保持愿逻辑
+        if (hitBlackCityId(context)) {
+            return true;
+        }
+        //没有命中实验保持愿逻辑
+        if (!hitExp(context)) {
+            return true;
+        }
+        //神券还可膨胀，但膨胀后能不能形成更优算价组合
+        String magicalMemberCouponLabel = extendDisplayInfo.get(MAGICAL_MEMBER_COUPON_LABEL);
+        if (StringUtils.isBlank(magicalMemberCouponLabel)) {
+            return true;
+        }
+        MagicalMemberTagTextDTO magicalMemberTagTextDTO = GsonUtils.fromJson(magicalMemberCouponLabel, MagicalMemberTagTextDTO.class);
+        if (Objects.isNull(magicalMemberTagTextDTO)) {
+            return true;
+        }
+        return Objects.equals(magicalMemberTagTextDTO.getShowType(), MagicalMemberTagShowTypeEnum.INFLATED_IS_BETTER_MMC.getValue());
+    }
+
+    public static boolean hitExp(ActivityCxt context) {
+        //以后下掉实验需要排出类目[主] 购物(379)-电子数码(649)//[主] 家居(600)-家用电器(604)见ShelfDouHuFetcher
+        if (Objects.isNull(magicalMemberConfig)) {
+            return false;
+        }
+        List<String> expSkWhitelist = magicalMemberConfig.getExpSkWhitelist();
+        //实验限制
+        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
+        if (DouHuUtils.hitAnySk(douHuMList, expSkWhitelist)) {
+            return true;
+        }
+        return false;
+    }
+
+    public static boolean hitExp(ActivityContext context) {
+        //以后下掉实验需要排出类目[主] 购物(379)-电子数码(649)//[主] 家居(600)-家用电器(604)见ShelfDouHuFetcher
+        if (Objects.isNull(magicalMemberConfig)) {
+            return false;
+        }
+        List<String> expSkWhitelist = magicalMemberConfig.getExpSkWhitelist();
+        //实验限制
+        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
+        if (DouHuUtils.hitAnySk(douHuMList, expSkWhitelist)) {
+            return true;
+        }
+        return false;
+    }
+
+    public static boolean hitClientType(ActivityCxt context) {
+        if (Objects.isNull(magicalMemberConfig)) {
+            return false;
+        }
+        List<Integer> clientType = magicalMemberConfig.getClientType();
+        // clientType限制
+        int userAgent = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
+        if (CollectionUtils.isNotEmpty(clientType) && clientType.contains(userAgent)) {
+            return true;
+        }
+        return false;
+    }
+
+    public static boolean hitBlackCityId(ActivityCxt context) {
+        int dpCityId = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.dpCityId);
+        if (Objects.isNull(magicalMemberConfig)) {
+            return false;
+        }
+        if (CollectionUtils.isNotEmpty(magicalMemberConfig.getBlackDpCityIds()) && magicalMemberConfig.getBlackDpCityIds().contains(dpCityId)) {
+            return true;
+        }
+        //配置-1，全量下掉
+        if (CollectionUtils.isNotEmpty(magicalMemberConfig.getBlackDpCityIds()) && magicalMemberConfig.getBlackDpCityIds().contains(-1L)) {
+            return true;
+        }
+        return false;
+    }
+
     @Data
     public static class MagicalMemberStyleConfig{
 
@@ -136,4 +236,29 @@ public class MagicalMemberTagUtils {
         private List<String> styleCExp;
     }
 
+    @Data
+    public static class MagicalMemberConfig {
+
+        /**
+         * 价格前缀神会员样式文案
+         */
+        private String magicalMember;
+        /**
+         * 神会员最低货架版本
+         */
+        private int lowestShelfVersion;
+        /**
+         * 端限制 :200美团app
+         */
+        private List<Integer> clientType;
+        /**
+         * 实验白名单
+         */
+        private List<String> expSkWhitelist;
+        /**
+         * 黑名单城市
+         */
+        private List<Integer> blackDpCityIds;
+    }
+
 }
