diff --git a/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOptTest.java b/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOptTest.java
index 5147d96707..0030e505d1 100644
--- a/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOptTest.java
+++ b/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/DefaultSalePricePrefixOptTest.java
@@ -2,27 +2,38 @@ package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepri
 
 import com.sankuai.athena.viewscene.framework.ActivityCxt;
 import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePricePrefixVP;
+import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
+import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
+import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
+import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
+import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
 import org.junit.Before;
 import org.junit.Test;
-import org.mockito.Mock;
-import org.mockito.MockitoAnnotations;
+import org.junit.runner.RunWith;
+import org.mockito.*;
+import org.mockito.junit.MockitoJUnitRunner;
 
+import static org.junit.Assert.assertEquals;
 import static org.junit.Assert.assertNull;
+import static org.mockito.ArgumentMatchers.*;
+import static org.mockito.Mockito.when;
 
 /**
  * 测试DefaultSalePricePrefixOpt类的compute方法
  */
+@RunWith(MockitoJUnitRunner.class)
 public class DefaultSalePricePrefixOptTest {
 
     @Mock
     private ActivityCxt mockActivityCxt;
 
+    @InjectMocks
     private DefaultSalePricePrefixOpt defaultSalePricePrefixOpt;
 
     @Before
     public void setUp() {
         MockitoAnnotations.initMocks(this);
-        defaultSalePricePrefixOpt = new DefaultSalePricePrefixOpt();
+        DefaultSalePricePrefixOpt.magicalMemberConfig = new MagicalMemberTagUtils.MagicalMemberConfig();
     }
 
     /**
@@ -39,4 +50,114 @@ public class DefaultSalePricePrefixOptTest {
         // assert
         assertNull("compute方法应该返回null", result);
     }
+
+
+    /**
+     * 测试compute方法在未命中实验时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenNotHitExp() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(false);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("未命中实验时应该返回null", result);
+        }
+    }
+
+    /**
+     * 测试compute方法在未命中客户端类型时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenNotHitClientType() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(false);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("未命中客户端类型时应该返回null", result);
+        }
+    }
+
+    /**
+     * 测试compute方法在命中黑名单城市时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenHitBlackCityId() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(true);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中黑名单城市时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testSatisfiedShelfVersion() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(false);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中最低版本限制时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testPromoCombinationWithMagicalMemberCoupon() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(false);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中最低版本限制时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testIsDoubleColumnShelf() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
+            MockedStatic<ParamsUtil> mockParamsUtil = Mockito.mockStatic(ParamsUtil.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);
+            mockParamsUtil.when(() -> ParamsUtil.getIntSafely(mockActivityCxt, ShelfActivityConstants.Params.doubleColumnShelf)).thenReturn(1);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中双列货架时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testIs() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
+            MockedStatic<ParamsUtil> mockParamsUtil = Mockito.mockStatic(ParamsUtil.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);
+            mockParamsUtil.when(() -> ParamsUtil.getIntSafely(mockActivityCxt, ShelfActivityConstants.Params.doubleColumnShelf)).thenReturn(0);
+            ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();
+            DefaultSalePricePrefixOpt.magicalMemberConfig.setMagicalMember("神券价");
+            String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertEquals("神券价", result);
+        }
+    }
 }
