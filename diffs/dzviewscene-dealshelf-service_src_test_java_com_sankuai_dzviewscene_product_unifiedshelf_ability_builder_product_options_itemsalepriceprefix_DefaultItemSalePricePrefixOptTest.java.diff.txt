diff --git a/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOptTest.java b/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOptTest.java
index c2451c26f1..ba2ac47da4 100644
--- a/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOptTest.java
+++ b/dzviewscene-dealshelf-service/src/test/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOptTest.java
@@ -1,17 +1,27 @@
 package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;
 
 import com.sankuai.athena.viewscene.framework.ActivityCxt;
+import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePricePrefixVP;
+import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceprefix.DefaultSalePricePrefixOpt;
+import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
 import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
+import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
+import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
 import org.junit.Before;
 import org.junit.Test;
-import org.mockito.Mock;
-import org.mockito.MockitoAnnotations;
+import org.junit.runner.RunWith;
+import org.mockito.*;
+import org.mockito.junit.MockitoJUnitRunner;
 
+import static org.junit.Assert.assertEquals;
 import static org.junit.Assert.assertNull;
+import static org.mockito.ArgumentMatchers.any;
+import static org.mockito.ArgumentMatchers.anyInt;
 
 /**
  * 测试DefaultItemSalePricePrefixOpt类的compute方法
  */
+@RunWith(MockitoJUnitRunner.class)
 public class DefaultItemSalePricePrefixOptTest {
 
     @Mock
@@ -19,12 +29,13 @@ public class DefaultItemSalePricePrefixOptTest {
     @Mock
     private UnifiedShelfSalePricePrefixVP.Param mockParam;
 
+    @InjectMocks
     private DefaultItemSalePricePrefixOpt defaultItemSalePricePrefixOpt;
 
     @Before
     public void setUp() {
         MockitoAnnotations.initMocks(this);
-        defaultItemSalePricePrefixOpt = new DefaultItemSalePricePrefixOpt();
+        DefaultItemSalePricePrefixOpt.magicalMemberConfig = new MagicalMemberTagUtils.MagicalMemberConfig();
     }
 
     /**
@@ -41,4 +52,113 @@ public class DefaultItemSalePricePrefixOptTest {
         // assert
         assertNull("compute方法应该返回null", result);
     }
+
+    /**
+     * 测试compute方法在未命中实验时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenNotHitExp() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(false);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("未命中实验时应该返回null", result);
+        }
+    }
+
+    /**
+     * 测试compute方法在未命中客户端类型时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenNotHitClientType() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(false);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("未命中客户端类型时应该返回null", result);
+        }
+    }
+
+    /**
+     * 测试compute方法在命中黑名单城市时返回null
+     */
+    @Test
+    public void testComputeReturnsNullWhenHitBlackCityId() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class)){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(true);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中黑名单城市时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testSatisfiedShelfVersion() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(false);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中最低版本限制时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testPromoCombinationWithMagicalMemberCoupon() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(false);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中最低版本限制时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testIsDoubleColumnShelf() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
+            MockedStatic<ItemSalePricePrefixVP> ItemSalePricePrefixVPMockedStatic = Mockito.mockStatic(ItemSalePricePrefixVP.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);
+            ItemSalePricePrefixVPMockedStatic.when(() -> ItemSalePricePrefixVP.isDoubleColumnShelf(mockActivityCxt)).thenReturn(true);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertNull("命中双列货架时应该返回null", result);
+        }
+    }
+
+    @Test
+    public void testIs() {
+        try(MockedStatic<MagicalMemberTagUtils> mockedStatic = Mockito.mockStatic(MagicalMemberTagUtils.class);
+            MockedStatic<MagicMemberUtil> mockMagicMemberUtils = Mockito.mockStatic(MagicMemberUtil.class);
+            MockedStatic<DzPromoUtils> mockPromoUtils = Mockito.mockStatic(DzPromoUtils.class);
+            MockedStatic<ItemSalePricePrefixVP> ItemSalePricePrefixVPMockedStatic = Mockito.mockStatic(ItemSalePricePrefixVP.class);){
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitExp(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitClientType(mockActivityCxt)).thenReturn(true);
+            mockedStatic.when(() -> MagicalMemberTagUtils.hitBlackCityId(mockActivityCxt)).thenReturn(false);
+            mockMagicMemberUtils.when(() -> MagicMemberUtil.satisfiedShelfVersion(anyInt(),anyInt())).thenReturn(true);
+            mockPromoUtils.when(() -> DzPromoUtils.promoCombinationWithMagicalMemberCoupon(any())).thenReturn(true);
+            ItemSalePricePrefixVPMockedStatic.when(() -> ItemSalePricePrefixVP.isDoubleColumnShelf(mockActivityCxt)).thenReturn(false);
+            UnifiedShelfSalePricePrefixVP.Param param = UnifiedShelfSalePricePrefixVP.Param.builder().build();
+            DefaultItemSalePricePrefixOpt.magicalMemberConfig.setMagicalMember("神券价");
+            String result = defaultItemSalePricePrefixOpt.compute(mockActivityCxt, param, null);
+            assertEquals("神券价", result);
+        }
+    }
 }
