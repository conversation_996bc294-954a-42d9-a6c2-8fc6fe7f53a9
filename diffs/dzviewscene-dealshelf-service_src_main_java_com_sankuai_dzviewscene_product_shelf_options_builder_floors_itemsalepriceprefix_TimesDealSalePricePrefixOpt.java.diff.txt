diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/TimesDealSalePricePrefixOpt.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/TimesDealSalePricePrefixOpt.java
index 0b97cf54c2..93adf29afb 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/TimesDealSalePricePrefixOpt.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/shelf/options/builder/floors/itemsalepriceprefix/TimesDealSalePricePrefixOpt.java
@@ -12,6 +12,7 @@ import org.apache.commons.collections.MapUtils;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
+import org.apache.commons.lang3.StringUtils;
 
 /**
  * @author: created by hang.yu on 2024/5/29 16:56
@@ -33,6 +34,11 @@ public class TimesDealSalePricePrefixOpt extends ItemSalePricePrefixVP<TimesDeal
         if (param.getProductM().isTimesDeal()) {
             return "单次";
         }
+        // 次卡优先级>神券价
+        String pricePrefix = getPricePrefix(context, param);
+        if (StringUtils.isNotBlank(pricePrefix)) {
+            return pricePrefix;
+        }
         return null;
     }
 
