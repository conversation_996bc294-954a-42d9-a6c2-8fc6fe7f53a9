diff --git a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOpt.java b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOpt.java
index 109c4817b0..b0f94ff551 100644
--- a/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOpt.java
+++ b/dzviewscene-dealshelf-service/src/main/java/com/sankuai/dzviewscene/product/unifiedshelf/ability/builder/product/options/itemsalepriceprefix/DefaultItemSalePricePrefixOpt.java
@@ -1,19 +1,38 @@
 package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepriceprefix;
 
+import com.sankuai.athena.inf.config.ConfigValue;
 import com.sankuai.athena.viewscene.framework.ActivityCxt;
+import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
 import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
+import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
+import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceprefix.DefaultSalePricePrefixOpt;
+import com.sankuai.dzviewscene.product.shelf.utils.MagicalMemberTagUtils;
 import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfSalePricePrefixVP;
+import com.sankuai.dzviewscene.product.utils.PriceUtils;
+import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
+import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
+import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
+import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
+import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
+import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
+import lombok.Data;
 
 @VPointOption(name = "默认-空变化点",
         description = "默认返回null",
         code = DefaultItemSalePricePrefixOpt.CODE,
         isDefault = true)
-public class DefaultItemSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<Void> {
+public class DefaultItemSalePricePrefixOpt extends UnifiedShelfSalePricePrefixVP<DefaultItemSalePricePrefixOpt.Config> {
 
     public static final String CODE = "DefaultItemSalePricePrefixOpt";
 
     @Override
-    public String compute(ActivityCxt context, Param param, Void config) {
-        return null;
+    public String compute(ActivityCxt context, Param param, Config config) {
+        return getPricePrefix(context, param);
+    }
+
+    @VPointCfg
+    @Data
+    public static class Config {
+
     }
 }
\ No newline at end of file
