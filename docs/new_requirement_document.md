# 团购货架神券价格体系需求文档 (重构版)

## 1. 需求背景与业务目标

### 1.1 业务背景
神券作为美团点评的核心金融产品，具有复杂的膨胀机制和算价逻辑。为了提升神券的用户感知和转化效果，需要在团购货架层面建立清晰的价格心智和标签体系。

### 1.2 业务目标
- **价格心智建立**: 通过"神券价"前缀区分神券优惠和普通优惠
- **膨胀引导优化**: 通过标签展示引导用户进行神券膨胀
- **数据监控完善**: 通过埋点数据监控神券功能效果
- **用户体验提升**: 提供清晰的神券价值感知

## 2. 核心功能需求

### 2.1 神券价格前缀展示

#### 2.1.1 功能描述
在团购货架的商品价格前增加"神券价"前缀，区分包含神券的优惠组合。

#### 2.1.2 技术实现要求
- **实现方式**: 通过VP(Variation Point)机制扩展价格展示能力
- **VP设计**: 新增`ItemSalePricePrefixVP`和`UnifiedShelfSalePricePrefixVP`
- **代码复用**: 通过静态方法实现老货架和统一货架的逻辑复用
- **职责分离**: 价格前缀作为独立的展示逻辑，不修改价格计算逻辑

#### 2.1.3 业务规则
```java
// 展示条件（所有条件必须同时满足）
1. 实验开关: hitExp(context) == true
2. 客户端支持: hitClientType(context) == true  
3. 城市控制: hitBlackCityId(context) == false
4. 版本要求: satisfiedShelfVersion(shelfVersion, lowestShelfVersion) == true
5. 神券判断: promoCombinationWithMagicalMemberCoupon(promoPriceM) == true
6. 货架类型: isDoubleColumnShelf(context) == false

// 展示效果
满足条件: "神券价 ¥29.9"
不满足条件: "¥29.9"
```

#### 2.1.4 覆盖范围
- ✅ **包含**: 单列货架（老货架 + POI一致性货架）
- ✅ **包含**: 货架二级落地页
- ❌ **排除**: 双列货架（空间限制）
- ❌ **排除**: 次卡商品（业务特殊性）

### 2.2 神券标签展示优化

#### 2.2.1 功能描述
基于神券膨胀状态和服务端算价结果，展示不同的神券标签文案。

#### 2.2.2 技术实现要求
- **实现方式**: 扩展现有`MagicalMemberPromoTagStrategy`
- **数据来源**: 依赖服务端`extendDisplayInfo`中的`magicalMemberCouponLabel`字段
- **判断逻辑**: 通过`MagicalMemberTagShowTypeEnum.INFLATED_IS_BETTER_MMC`判断膨胀优势

#### 2.2.3 展示规则

##### 单列货架标签规则
```java
// 普通神券商家（不可膨胀）
"神券icon（无膨胀箭头）已减X，共省Y"

// 膨胀神券商家（已膨胀或不可继续膨胀）
"神券icon（有膨胀箭头）已减X，共省Y"

// 膨胀神券商家（可膨胀且膨胀后更优）
"神券icon（有膨胀箭头）最高膨至X|膨胀"
```

##### 双列货架标签规则
```java
// 普通神券商家
"神券icon（无膨胀箭头）共省Y"

// 膨胀神券商家  
"神券icon（有膨胀箭头）共省Y"
```

#### 2.2.4 核心判断逻辑
```java
// 膨胀展示判断的核心逻辑
boolean canInflate = 从promotionOtherInfoMap获取
boolean afterInflate = 从promotionOtherInfoMap获取  
boolean canInflateAndCanInflateMore = 从服务端extendDisplayInfo获取

// 展示逻辑
if (hasInflateInfo && canInflate && canInflateAndCanInflateMore) {
    // 展示膨胀引导文案
    return "最高膨至X|膨胀";
} else {
    // 展示已减共省文案  
    return "已减X，共省Y";
}
```

### 2.3 埋点数据增强

#### 2.3.1 功能描述
在现有Ocean埋点基础上增加`price_title`字段，用于监控神券价格前缀的展示效果。

#### 2.3.2 技术实现要求
- **实现方式**: 扩展`UnifiedShelfCommonOcean.paddingProductLabs()`方法
- **数据来源**: 直接使用`itemVO.getSalePricePrefix()`保证数据一致性
- **字段定义**: 新增`OceanConstantUtils.PRICE_TITLE`常量

#### 2.3.3 埋点规则
```java
// 埋点逻辑
private static Object getPriceTitle(ShelfItemVO itemVO) {
    String pricePrefix = itemVO.getSalePricePrefix();
    if (StringUtils.isNotEmpty(pricePrefix) && "神券价".equals(pricePrefix)) {
        return "神券价";
    }
    return StringUtils.EMPTY; // 注意：返回空字符串而不是"-999"
}
```

## 3. 配置与控制机制

### 3.1 统一配置结构

#### 3.1.1 配置定义
```java
@ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.config1", defaultValue = "{}")
public static MagicalMemberConfig magicalMemberConfig;

@Data
public static class MagicalMemberConfig {
    private String magicalMember = "神券价";           // 前缀文案
    private int lowestShelfVersion = 100;             // 最低支持版本
    private List<Integer> clientType = Arrays.asList(200); // 客户端类型（200=美团APP）
    private List<String> expSkWhitelist = Arrays.asList(); // 实验白名单
    private List<Integer> blackDpCityIds = Arrays.asList(); // 城市黑名单
}
```

#### 3.1.2 配置优先级
```java
// 配置检查优先级（按顺序执行，任一失败则不展示）
1. 实验控制 (expSkWhitelist)
2. 客户端控制 (clientType) 
3. 城市控制 (blackDpCityIds)
4. 版本控制 (lowestShelfVersion)
5. 业务逻辑控制 (神券判断 + 货架类型)
```

### 3.2 多维度控制策略

#### 3.2.1 实验控制
- **斗斛实验**: 通过`expSkWhitelist`控制实验范围
- **灰度发布**: 支持按实验分组渐进式发布
- **快速回滚**: 通过修改实验配置快速回滚

#### 3.2.2 平台控制
- **客户端限制**: 仅支持美团APP(200)，后续可扩展点评
- **版本兼容**: 通过`lowestShelfVersion`控制最低支持版本
- **城市控制**: 支持城市级别的功能开关和紧急下线

#### 3.2.3 风险控制
```java
// 紧急下线机制
if (blackDpCityIds.contains(-1)) {
    return false; // 全量下线
}

// 城市级下线
if (blackDpCityIds.contains(dpCityId)) {
    return false; // 指定城市下线
}
```

## 4. 架构设计要求

### 4.1 VP机制设计

#### 4.1.1 VP定义规范
```java
// 老货架VP
@VPoint(name = "商品价格前缀", description = "支持价格前缀展示", code = "ItemSalePricePrefixVP")
public abstract class ItemSalePricePrefixVP<T> extends PmfVPoint<String, ItemSalePricePrefixVP.Param, T> {
    
    // 静态方法复用逻辑
    public static String getPricePrefix(ActivityCxt context, Param param) {
        // 统一的判断逻辑
    }
}

// 统一货架VP
@VPoint(name = "统一货架价格前缀", description = "支持统一货架价格前缀展示", code = "UnifiedShelfSalePricePrefixVP")  
public abstract class UnifiedShelfSalePricePrefixVP<T> extends PmfVPoint<String, UnifiedShelfSalePricePrefixVP.Param, T> {
    
    // 复用老货架的静态方法
    public static String getPricePrefix(ActivityCxt context, Param param) {
        return ItemSalePricePrefixVP.getPricePrefix(context, param);
    }
}
```

#### 4.1.2 VP实现规范
```java
// 默认实现
@VPointOption(name = "默认价格前缀", code = "DefaultSalePricePrefixOpt", isDefault = true)
public class DefaultSalePricePrefixOpt extends ItemSalePricePrefixVP<DefaultSalePricePrefixOpt.Config> {
    
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return getPricePrefix(context, param); // 调用静态方法
    }
    
    @VPointCfg
    @Data
    public static class Config {
        // 组件级配置（如果需要）
    }
}
```

### 4.2 数据流设计

#### 4.2.1 单向数据流
```java
// 数据流转路径
配置中心 -> MagicalMemberConfig
    ↓
VP判断逻辑 -> getPricePrefix()
    ↓  
VO数据设置 -> itemVO.setSalePricePrefix()
    ↓
前端展示 -> "神券价 ¥29.9"
    ↓
埋点上报 -> price_title: "神券价"
```

#### 4.2.2 数据一致性保证
```java
// 一致性保证机制
1. 单一数据源: getPricePrefix()统一判断逻辑
2. 数据传递: 通过VO对象传递，避免重复计算  
3. 埋点复用: 直接使用itemVO.getSalePricePrefix()
4. 校验机制: 确保展示数据和埋点数据一致
```

## 5. 边界条件与异常处理

### 5.1 商品类型边界

#### 5.1.1 次卡商品处理
- **特殊VP**: `TimesCardSalePricePrefixOpt`和`TimesDealSalePricePrefixOpt`
- **处理逻辑**: 次卡商品有独立的价格前缀逻辑，不参与神券价格体系
- **兼容性**: 保持现有次卡商品的展示逻辑不变

#### 5.1.2 货架类型边界
```java
// 双列货架判断
public static boolean isDoubleColumnShelf(ActivityCxt context) {
    int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
    return doubleColumnShelf == 1;
}

// 双列货架不展示价格前缀，但神券标签正常展示
```

### 5.2 异常处理机制

#### 5.2.1 配置异常处理
```java
// 配置为空的处理
if (Objects.isNull(magicalMemberConfig)) {
    return null; // 不展示前缀
}

// 配置字段缺失的处理  
String magicalMember = StringUtils.defaultIfEmpty(
    magicalMemberConfig.getMagicalMember(), 
    "神券价" // 默认文案
);
```

#### 5.2.2 数据异常处理
```java
// 优惠数据异常处理
try {
    ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(productM, cardM);
    if (promoPriceM == null) {
        return null; // 无优惠数据时不展示前缀
    }
} catch (Exception e) {
    log.error("获取优惠价格失败", e);
    return null; // 异常时不展示前缀
}
```

## 6. 测试要求

### 6.1 功能测试

#### 6.1.1 价格前缀测试
- **正向测试**: 验证满足所有条件时正确展示"神券价"前缀
- **边界测试**: 验证各种边界条件下的展示逻辑
- **异常测试**: 验证异常情况下的降级处理

#### 6.1.2 配置控制测试
- **实验控制**: 验证斗斛实验开关的有效性
- **平台控制**: 验证客户端类型和版本控制
- **城市控制**: 验证城市黑名单和紧急下线

### 6.2 集成测试

#### 6.2.1 数据一致性测试
- **展示一致性**: 验证前端展示和埋点数据的一致性
- **跨货架一致性**: 验证老货架和统一货架的逻辑一致性
- **跨平台一致性**: 验证美团和点评的差异化逻辑

#### 6.2.2 性能测试
- **响应时间**: 验证新增逻辑对响应时间的影响
- **并发性能**: 验证高并发场景下的性能表现
- **资源消耗**: 验证内存和CPU资源消耗

## 7. 发布计划

### 7.1 分阶段发布

#### 7.1.1 阶段一：基础功能（灰度10%）
- 神券价格前缀展示
- 基础配置控制
- 埋点数据上报

#### 7.1.2 阶段二：标签优化（灰度30%）
- 神券标签展示优化
- 膨胀引导逻辑
- 双列货架适配

#### 7.1.3 阶段三：全量发布（100%）
- 功能全量开放
- 监控数据分析
- 效果评估优化

### 7.2 风险控制

#### 7.2.1 回滚机制
- **配置回滚**: 通过修改配置快速回滚功能
- **实验回滚**: 通过斗斛实验快速下线
- **紧急下线**: 通过城市黑名单紧急下线

#### 7.2.2 监控告警
- **功能监控**: 监控神券价格前缀的展示率
- **异常监控**: 监控配置异常和数据异常
- **性能监控**: 监控响应时间和错误率

## 8. 成功标准

### 8.1 技术指标
- **功能正确性**: 神券价格前缀展示准确率 > 99%
- **性能影响**: 响应时间增加 < 10ms
- **稳定性**: 错误率 < 0.1%

### 8.2 业务指标
- **用户感知**: 神券价格心智建立效果
- **转化提升**: 神券相关转化率提升
- **用户体验**: 用户满意度和反馈

通过这个重构的需求文档，技术方案设计将更加准确地遵循现有架构模式，充分利用VP机制，保证数据一致性，并建立完善的配置控制和风险管控机制。
