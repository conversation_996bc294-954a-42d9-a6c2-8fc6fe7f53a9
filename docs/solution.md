# 团购货架神券价格体系技术实现方案

## 1. 方案概述

本方案基于现有UnifiedShelfService架构，通过扩展现有的价格计算、标签构建、埋点等模块，实现神券价格体系分层、神券标签优化、SPU货架活动标题传参、特团氛围下线和埋点增强等功能。

## 2. 核心功能实现

### 2.1 神券价格体系分层

#### 2.1.1 价格计算逻辑扩展

**涉及类**: `PriceDisplayUtils`、`PriceUtils`

**实现方案**:
1. 扩展`PriceDisplayUtils.getSalePrice()`方法，增加神券价格判断逻辑
2. 新增`getSalePriceWithMagicalCouponPrefix()`方法

```java
public static String getSalePriceWithMagicalCouponPrefix(ActivityCxt context, ProductM productM) {
    // 1. 获取最优优惠价格组合
    CardM cardM = context.getSource(CardFetcher.CODE);
    ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(productM, cardM);
    
    // 2. 判断是否包含神券
    boolean hasMagicalCoupon = DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM);
    
    // 3. 获取基础价格
    String basePrice = getSalePrice(context, productM);
    
    // 4. 根据神券状态返回价格
    if (hasMagicalCoupon && isShowMagicalCouponPrice(context, productM)) {
        return "神券价 " + basePrice;
    }
    return basePrice;
}

private static boolean isShowMagicalCouponPrice(ActivityCxt context, ProductM productM) {
    // 1. 次卡商品不展示神券价格
    if (productM.getProductType() == ProductTypeEnum.TIME_CARD.getType()) {
        return false;
    }
    
    // 2. 双列货架不展示神券价格
    int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
    if (doubleColumnShelf == 1) {
        return false;
    }
    
    // 3. 通过实验控制
    List<DouHuM> douHuList = context.getSource(ShelfDouHuFetcher.CODE);
    return DouHuUtils.hitAnySk(douHuList, "magical_coupon_price_exp");
}
```

#### 2.1.2 商品区价格展示

**涉及类**: `UnifiedProductAreaBuilder`

**实现方案**:
1. 新增`UnifiedShelfItemSalePriceVP`变化点
2. 创建`MagicalCouponSalePriceOpt`选项类

```java
@VPointOption(name = "神券价格展示选项", 
              description = "支持神券价格前缀展示", 
              code = "MagicalCouponSalePriceOpt")
public class MagicalCouponSalePriceOpt extends UnifiedShelfItemSalePriceVP<MagicalCouponSalePriceOpt.Config> {
    
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return PriceDisplayUtils.getSalePriceWithMagicalCouponPrefix(context, param.getProductM());
    }
    
    @VPointCfg
    @Data
    public static class Config {
        private boolean enableMagicalCouponPrice = true;
        private List<String> excludeSceneCodes = Lists.newArrayList();
    }
}
```

### 2.2 神券标签展示优化

#### 2.2.1 神券标签文案逻辑

**涉及类**: `MagicalMemberPromoTagStrategy`

**实现方案**:
扩展现有的`buildCommonTagText()`方法，支持膨胀状态判断

```java
private String buildCommonTagText(PriceBottomTagBuildReq req, ProductPromoPriceM promoPriceM) {
    PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(promoPriceM);
    if (magicalItemM == null) {
        return buildPromoTag(req, promoPriceM);
    }
    
    Map<String, String> extraInfo = Optional.ofNullable(magicalItemM.getPromotionOtherInfoMap())
                                           .orElse(Maps.newHashMap());
    boolean canInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
    boolean afterInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
    
    // 1. 普通神券商家 - 不可膨胀
    if (!canInflate) {
        BigDecimal reducedAmount = magicalItemM.getAmount();
        BigDecimal totalSaved = promoPriceM.getTotalPromoPrice();
        return String.format("已减%s，共省%s", 
                           reducedAmount.stripTrailingZeros().toPlainString(),
                           totalSaved.stripTrailingZeros().toPlainString());
    }
    
    // 2. 膨胀神券商家 - 已膨胀或不可继续膨胀
    if (afterInflate || !canInflateMore(magicalItemM)) {
        BigDecimal reducedAmount = magicalItemM.getAmount();
        BigDecimal totalSaved = promoPriceM.getTotalPromoPrice();
        return String.format("已减%s，共省%s", 
                           reducedAmount.stripTrailingZeros().toPlainString(),
                           totalSaved.stripTrailingZeros().toPlainString());
    }
    
    // 3. 膨胀神券商家 - 可膨胀且膨胀后更优
    if (canInflateAndBetter(req.getContext(), magicalItemM)) {
        int maxInflateAmount = NumberUtils.toInt(extraInfo.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue()));
        BigDecimal maxInflateDecimal = new BigDecimal(maxInflateAmount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return String.format("最高膨至%s|膨胀", maxInflateDecimal.stripTrailingZeros().toPlainString());
    }
    
    // 默认返回已减共省
    return buildDefaultReducedAndSavedText(magicalItemM, promoPriceM);
}

private boolean canInflateMore(PromoItemM magicalItemM) {
    // 判断是否还可以继续膨胀的逻辑
    Map<String, String> extraInfo = magicalItemM.getPromotionOtherInfoMap();
    int currentAmount = magicalItemM.getAmount().multiply(new BigDecimal(100)).intValue();
    int maxInflateAmount = NumberUtils.toInt(extraInfo.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue()));
    return currentAmount < maxInflateAmount;
}

private boolean canInflateAndBetter(ActivityCxt context, PromoItemM magicalItemM) {
    // 判断膨胀后是否能形成更优算价组合
    // 这里需要调用膨胀服务获取膨胀后的价格，然后比较
    return true; // 简化实现，实际需要调用膨胀服务
}
```

#### 2.2.2 双列货架标签处理

**实现方案**:
在`useNormalMagicalTag()`方法中已有双列货架判断，需要扩展文案逻辑

```java
private String buildDoubleColumnTagText(ProductPromoPriceM promoPriceM, PromoItemM magicalItemM) {
    Map<String, String> extraInfo = Optional.ofNullable(magicalItemM.getPromotionOtherInfoMap())
                                           .orElse(Maps.newHashMap());
    boolean canInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
    
    BigDecimal totalSaved = promoPriceM.getTotalPromoPrice();
    
    if (canInflate) {
        return String.format("神券icon（有膨胀箭头）共省%s", totalSaved.stripTrailingZeros().toPlainString());
    } else {
        return String.format("神券icon（无膨胀箭头）共省%s", totalSaved.stripTrailingZeros().toPlainString());
    }
}
```

### 2.3 SPU货架活动标题传参

#### 2.3.1 活动数据结构扩展

**涉及类**: `ProductActivityM`

**实现方案**:
扩展现有的活动数据结构，增加活动标题相关字段

```java
public class ProductActivityM {
    // 现有字段...
    
    /**
     * 活动标题文字
     */
    private String activityTitleText;
    
    /**
     * 活动标题icon URL
     */
    private String activityTitleIcon;
    
    /**
     * 活动标题icon宽度
     */
    private Integer activityTitleIconWidth;
    
    /**
     * 活动标题icon高度
     */
    private Integer activityTitleIconHeight;
}
```

#### 2.3.2 活动标题展示逻辑

**涉及类**: `UnifiedShelfItemTitleVP`

**实现方案**:
新增SPU货架活动标题处理选项

```java
@VPointOption(name = "SPU货架活动标题", 
              description = "支持SPU货架活动标题展示", 
              code = "SpuShelfActivityTitleOpt")
public class SpuShelfActivityTitleOpt extends UnifiedShelfItemTitleVP<SpuShelfActivityTitleOpt.Config> {
    
    @Override
    public List<StyleTextModel> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        
        // 1. 获取基础标题
        String baseTitle = getBaseTitle(productM);
        
        // 2. 获取活动信息
        List<ProductActivityM> activities = getProductActivities(context, productM);
        
        // 3. 构建标题模型
        List<StyleTextModel> titleModels = Lists.newArrayList();
        
        // 4. 添加活动标题（如果存在）
        ProductActivityM mainActivity = getMainActivity(activities);
        if (mainActivity != null && StringUtils.isNotEmpty(mainActivity.getActivityTitleText())) {
            StyleTextModel activityTitle = buildActivityTitleModel(mainActivity);
            titleModels.add(activityTitle);
        }
        
        // 5. 添加基础标题
        StyleTextModel baseModel = new StyleTextModel();
        baseModel.setText(baseTitle);
        baseModel.setTextColor("#333333");
        titleModels.add(baseModel);
        
        return titleModels;
    }
    
    private StyleTextModel buildActivityTitleModel(ProductActivityM activity) {
        StyleTextModel model = new StyleTextModel();
        model.setText(activity.getActivityTitleText());
        model.setTextColor("#FFFFFF");
        model.setBackgroundColor("#FF6633");
        model.setBorderRadius(4);
        model.setPadding("2,4,2,4");
        
        // 如果有icon，设置icon信息
        if (StringUtils.isNotEmpty(activity.getActivityTitleIcon())) {
            model.setIconUrl(activity.getActivityTitleIcon());
            model.setIconWidth(activity.getActivityTitleIconWidth());
            model.setIconHeight(activity.getActivityTitleIconHeight());
        }
        
        return model;
    }
    
    @VPointCfg
    @Data
    public static class Config {
        private boolean enableActivityTitle = true;
        private int maxTitleLength = 5;
        private int minTitleLength = 3;
    }
}
```

### 2.4 特团氛围下线

#### 2.4.1 筛选标签处理

**涉及类**: `UnifiedShelfMultiActivityTitleOpt`

**实现方案**:
扩展现有的多活动筛选图片按钮逻辑

```java
@Override
public IconRichLabelModel compute(ActivityCxt activityCxt, Param param, Config config) {
    FilterBtnM filterBtnM = param.getFilterBtnM();
    List<ProductActivityM> activities = param.getActivities();
    
    // 1. 判断是否为特价团购tab
    boolean isSpecialPriceTab = isSpecialPriceGroupBuyTab(filterBtnM);
    
    // 2. 判断是否有其他活动
    boolean hasOtherActivities = hasOtherActivities(activities, filterBtnM);
    
    IconRichLabelModel labelModel = new IconRichLabelModel();
    labelModel.setText(filterBtnM.getTitle());
    
    // 3. 特价团购tab处理逻辑
    if (isSpecialPriceTab) {
        if (hasOtherActivities) {
            // 有其他活动时不展示图片，仅展示文案
            labelModel.setIcon(null);
            labelModel.setTextColor("#333333");
        } else {
            // 无其他活动时正常展示
            labelModel.setIcon(getSpecialPriceIcon());
            labelModel.setTextColor("#FF6633");
        }
    } else {
        // 其他活动正常展示图片
        ProductActivityM activity = findActivityByFilterBtn(activities, filterBtnM);
        if (activity != null) {
            labelModel.setIcon(activity.getActivityTitleIcon());
            labelModel.setTextColor("#FF6633");
        }
    }
    
    return labelModel;
}

private boolean isSpecialPriceGroupBuyTab(FilterBtnM filterBtnM) {
    // 判断是否为特价团购tab的逻辑
    return "特价团购".equals(filterBtnM.getTitle()) || 
           "特惠".equals(filterBtnM.getTitle());
}

private boolean hasOtherActivities(List<ProductActivityM> activities, FilterBtnM currentFilter) {
    // 判断除当前筛选外是否还有其他活动
    return activities.stream()
                    .anyMatch(activity -> !activity.getTitle().equals(currentFilter.getTitle()));
}
```

### 2.5 埋点增强

#### 2.5.1 价格埋点字段扩展

**涉及类**: `UnifiedShelfItemCommonOceanLabsOpt`

**实现方案**:
扩展商品埋点，增加price_title字段

```java
@Override
public String compute(ActivityCxt context, Param param, Config config) {
    Map<String, Object> oceanMap = new HashMap<>();
    
    // 1. 现有埋点逻辑
    paddingBasicOceanInfo(oceanMap, param);
    
    // 2. 增加价格相关埋点
    paddingPriceOceanInfo(oceanMap, context, param);
    
    return JsonCodec.encode(oceanMap);
}

private void paddingPriceOceanInfo(Map<String, Object> oceanMap, ActivityCxt context, Param param) {
    ProductM productM = param.getProductM();
    
    // 1. 基础价格信息
    oceanMap.put("price", productM.getBasePriceTag());
    
    // 2. 价格标题信息
    String priceTitle = getPriceTitle(context, productM);
    oceanMap.put("price_title", priceTitle);
    
    // 3. 神券相关信息
    paddingMagicalCouponOceanInfo(oceanMap, context, productM);
}

private String getPriceTitle(ActivityCxt context, ProductM productM) {
    // 1. 判断是否展示神券价格
    if (isShowMagicalCouponPrice(context, productM)) {
        return "神券价";
    }
    
    // 2. 不展示神券价时返回默认值
    return "-999";
}

private void paddingMagicalCouponOceanInfo(Map<String, Object> oceanMap, ActivityCxt context, ProductM productM) {
    CardM cardM = context.getSource(CardFetcher.CODE);
    ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(productM, cardM);
    
    if (DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)) {
        PromoItemM magicalItemM = DzPromoUtils.getMagicalMemberPromoItem(promoPriceM);
        if (magicalItemM != null) {
            Map<String, String> extraInfo = magicalItemM.getPromotionOtherInfoMap();
            
            // 膨胀相关信息
            oceanMap.put("can_inflate", extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
            oceanMap.put("after_inflate", extraInfo.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
            oceanMap.put("max_inflate_money", extraInfo.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue()));
            
            // 神券金额信息
            oceanMap.put("magical_coupon_amount", magicalItemM.getAmount().stripTrailingZeros().toPlainString());
        }
    }
}
```

## 3. 配置和开关控制

### 3.1 实验配置

**Lion配置项**:
```properties
# 神券价格展示实验
magical.coupon.price.display.enable=true

# 神券标签优化实验  
magical.coupon.tag.optimize.enable=true

# SPU货架活动标题实验
spu.shelf.activity.title.enable=true

# 特团氛围下线实验
special.group.buy.atmosphere.disable=true
```

**斗斛实验配置**:
```json
{
  "magical_coupon_price_exp": ["sk1", "sk2"],
  "magical_coupon_tag_exp": ["sk3", "sk4"], 
  "spu_activity_title_exp": ["sk5", "sk6"],
  "special_group_atmosphere_exp": ["sk7", "sk8"]
}
```

### 3.2 场景控制

**场景码配置**:
```java
public class MagicalCouponSceneConfig {
    // 支持神券价格的场景
    public static final List<String> MAGICAL_PRICE_SCENES = Arrays.asList(
        "poi_deal_shelf",
        "poi_unified_shelf", 
        "deal_filter_list"
    );
    
    // 排除神券价格的场景
    public static final List<String> EXCLUDE_MAGICAL_PRICE_SCENES = Arrays.asList(
        "times_card_shelf",
        "double_column_shelf"
    );
}
```

## 4. 边界情况处理

### 4.1 商品类型边界

```java
public class MagicalCouponBoundaryChecker {
    
    public static boolean shouldShowMagicalPrice(ProductM productM, ActivityCxt context) {
        // 1. 次卡商品不展示
        if (productM.getProductType() == ProductTypeEnum.TIME_CARD.getType()) {
            return false;
        }
        
        // 2. 双列货架不展示
        int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
        if (doubleColumnShelf == 1) {
            return false;
        }
        
        // 3. 特定场景不展示
        String sceneCode = context.getSceneCode();
        if (MagicalCouponSceneConfig.EXCLUDE_MAGICAL_PRICE_SCENES.contains(sceneCode)) {
            return false;
        }
        
        // 4. 实验控制
        List<DouHuM> douHuList = context.getSource(ShelfDouHuFetcher.CODE);
        return DouHuUtils.hitAnySk(douHuList, "magical_coupon_price_exp");
    }
    
    public static boolean shouldShowMagicalTag(ProductM productM, ActivityCxt context) {
        // 神券标签展示边界判断
        return shouldShowMagicalPrice(productM, context);
    }
}
```

### 4.2 价格倒挂处理

```java
public class PriceHangingHandler {
    
    public static boolean hasPriceHanging(ProductM productM, CardM cardM) {
        // 使用现有的倒挂判断逻辑
        return CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices());
    }
    
    public static String handleHangingPrice(ProductM productM, CardM cardM) {
        if (hasPriceHanging(productM, cardM)) {
            // 倒挂时返回立减价格
            ProductPromoPriceM directPromo = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
            if (directPromo != null) {
                return directPromo.getPromoPriceTag();
            }
        }
        return productM.getBasePriceTag();
    }
}
```

## 5. 数据流转图

```
请求入口 (UnifiedShelfServiceImpl)
    ↓
活动引擎 (UnifiedShelfActivityEngine)
    ↓
上下文预处理 (PreHandlerContextAbility)
    ↓
实验配置获取 (ShelfDouHuFetcher)
    ↓
商品数据获取 (DealQueryFetcher → ProductPaddingFetcher)
    ↓
价格计算 (PriceUtils.getUserHasPromoPrice)
    ↓
神券判断 (DzPromoUtils.promoCombinationWithMagicalMemberCoupon)
    ↓
商品区构建 (UnifiedProductAreaBuilder)
    ↓
价格展示 (UnifiedShelfItemSalePriceVP)
    ↓
标签构建 (MagicalMemberPromoTagStrategy)
    ↓
埋点构建 (UnifiedShelfOceanBuilder)
    ↓
响应组装 (UnifiedShelfResponseAssembler)
```

## 6. 风险点和缓解措施

### 6.1 性能风险
- **风险**: 神券价格计算增加复杂度
- **缓解**: 增加缓存机制，批量处理优化

### 6.2 兼容性风险  
- **风险**: 老版本客户端兼容性
- **缓解**: 通过shelfVersion控制，渐进式升级

### 6.3 数据一致性风险
- **风险**: 价格和标签数据不一致
- **缓解**: 统一数据源，增加校验逻辑

## 7. 测试策略

### 7.1 单元测试
- 价格计算逻辑测试
- 神券判断逻辑测试  
- 标签文案生成测试
- 埋点数据构建测试

### 7.2 集成测试
- 完整调用链路测试
- 不同场景下的功能测试
- 边界条件测试

### 7.3 性能测试
- 大量商品处理性能测试
- 并发场景压力测试

## 8. 上线计划

### 8.1 分阶段上线
1. **阶段一**: 神券价格体系分层 (灰度10%)
2. **阶段二**: 神券标签展示优化 (灰度30%) 
3. **阶段三**: SPU活动标题和特团氛围 (灰度50%)
4. **阶段四**: 埋点增强和全量上线 (100%)

### 8.2 监控告警
- 价格计算异常监控
- 标签展示异常监控  
- 埋点上报监控
- 性能指标监控

## 9. 问题和建议

### 9.1 需求模糊点
1. **膨胀判断逻辑**: 需要明确"膨胀后能够形成更优算价组合"的具体判断标准
2. **活动标题优先级**: 当商品有多个活动时，活动标题的展示优先级规则需要明确
3. **特团氛围范围**: "特团氛围下线"的具体范围和影响面需要进一步确认

### 9.2 技术建议
1. **缓存策略**: 建议对神券价格计算结果进行缓存，提升性能
2. **配置化**: 建议将更多逻辑配置化，便于后续调整
3. **监控完善**: 建议增加详细的业务监控，及时发现问题

### 9.3 实现限制
1. **膨胀服务依赖**: 神券膨胀判断需要依赖外部膨胀服务，可能存在性能和稳定性风险
2. **前端兼容性**: 新增的价格前缀和活动标题需要前端配合支持
3. **数据源限制**: 活动标题数据需要从运营配置系统获取，可能存在数据延迟
