# 团购货架神券价格体系需求调用链路分析

## 需求概述

本次需求主要涉及团购货架的价格体系分层、神券标签优化、SPU货架活动标题传参、特团氛围下线以及埋点增强等功能。

## 核心调用链路分析

### 1. 神券价格体系分层调用链路

#### 1.1 请求入口
```
UnifiedShelfServiceImpl.queryShelfNavAndProduct()
  ↓
UnifiedShelfExecutor.execute()
  ↓
UnifiedShelfActivityEngine.execute()
```

#### 1.2 数据获取阶段
```
PreHandlerContextAbility (上下文预处理)
  ↓
ShelfDouHuFetcher (斗斛实验查询)
  ↓
FilterFirstFetcher (筛选器前置召回)
  ↓
DealQueryFetcher (团单召回)
  ↓
ProductPaddingFetcher (商品填充)
```

#### 1.3 价格计算核心链路
```
ProductPaddingFetcher.build()
  ↓
PriceUtils.getUserHasPromoPrice() // 获取用户最优优惠价格
  ↓
CardPromoUtils.getFirstUserHoldCardPromo() // 获取持卡优惠
  ↓
DzPromoUtils.promoCombinationWithMagicalMemberCoupon() // 判断是否包含神券
  ↓
PriceDisplayUtils.getSalePrice() // 计算最终到手价
```

#### 1.4 神券标签构建链路
```
UnifiedProductAreaBuilder.build()
  ↓
ItemPriceBottomTagsVP.execute() // 价格底部标签变化点
  ↓
MagicalMemberPromoTagStrategy.buildTag() // 神券标签策略
  ↓
buildMtNewStyleMagicalMemberTag() // 构建新样式神券标签
  ↓
buildCommonTagText() // 构建标签文案（已减X，共省Y 或 最高膨至X|膨胀）
```

### 2. SPU货架活动标题传参调用链路

#### 2.1 活动数据获取
```
ProductActivitiesFetcher.build()
  ↓
查询商品活动列表数据
  ↓
构造 List<ProductActivityM> (包含活动标题文字和活动标题icon)
```

#### 2.2 活动标题展示
```
UnifiedProductAreaBuilder.build()
  ↓
UnifiedShelfItemTitleVP.execute() // 商品标题变化点
  ↓
UnifiedShelfItemTrySpuNameTitleOpt.compute() // SPU名称标题选项
  ↓
getStandardSpuName() // 获取标准SPU名称
  ↓
处理活动标题文字展示逻辑
```

### 3. 特团氛围下线调用链路

#### 3.1 筛选标签处理
```
UnifiedShelfFilterBuilder.build()
  ↓
UnifiedShelfFilterTitleVP.execute() // 筛选按钮名字变化点
  ↓
UnifiedShelfMultiActivityTitleOpt.compute() // 多活动筛选图片按钮
  ↓
处理特价团购tab的展示逻辑（有其他活动时不展示图片，仅展示文案）
```

#### 3.2 商品标签优化
```
UnifiedProductAreaBuilder.build()
  ↓
ProductActivityTagsVP.execute() // 商品活动标签变化点
  ↓
各种ActivityTagsOpt实现类
  ↓
处理特团氛围下线后的商品标签展示
```

### 4. 埋点增强调用链路

#### 4.1 Ocean埋点构建
```
UnifiedShelfOceanBuilder.build()
  ↓
UnifiedShelfItemOceanLabsVP.execute() // 商品埋点变化点
  ↓
UnifiedShelfItemCommonOceanLabsOpt.compute() // 通用商品埋点
  ↓
buildProductLabs() // 构建商品埋点信息
  ↓
添加price_title字段（展示"神券价"时上报，否则返回"-999"）
```

#### 4.2 埋点数据填充
```
UnifiedShelfCommonOcean.paddingCommonOcean()
  ↓
paddingMagicalMemberLabs() // 神会员打点上报
  ↓
处理价格相关埋点字段
```

## 关键类和方法分析

### 1. 价格计算相关

#### PriceUtils.getUserHasPromoPrice()
- **功能**: 获取用户最优优惠价格组合
- **逻辑**: 
  1. 优先商家会员卡优惠
  2. 其次折扣卡、玩乐卡优惠
  3. 最后立减优惠
- **神券处理**: 通过DzPromoUtils.promoCombinationWithMagicalMemberCoupon()判断是否包含神券

#### PriceDisplayUtils.getSalePrice()
- **功能**: 计算商品最终到手价
- **逻辑**:
  1. 卡优惠价格（无倒挂）
  2. 玩美季活动价格
  3. 立减优惠价格
  4. 基础价格

### 2. 神券标签相关

#### MagicalMemberPromoTagStrategy.buildTag()
- **功能**: 构建神券优惠标签
- **逻辑**:
  1. 判断优惠组合是否包含神券
  2. 根据实验配置选择新旧样式
  3. 构建标签文案和样式

#### buildCommonTagText()
- **功能**: 构建神券标签文案
- **逻辑**:
  - 普通神券：【已减X，共省Y】
  - 膨胀神券（不可膨胀）：【已减X，共省Y】
  - 膨胀神券（可膨胀且更优）：【最高膨至X|膨胀】

### 3. 活动标题相关

#### ProductActivitiesFetcher.build()
- **功能**: 查询商品活动列表数据
- **返回**: List<ProductActivityM>，包含活动标题文字和活动标题icon

#### UnifiedShelfItemTitleVP相关Options
- **UnifiedShelfItemTrySpuNameTitleOpt**: 支持SPU名称标题
- **处理逻辑**: 根据配置决定是否使用标准SPU名称

## 边界情况处理

### 1. 价格展示边界
- **次卡商品**: ProductTypeEnum.TIME_CARD类型商品不参与神券价格体系
- **双列货架**: 通过showType控制，双列货架不展示神券价格分层
- **倒挂场景**: CardPromoUtils.isCardDaoGuaPromo()判断并处理倒挂情况

### 2. 活动标题边界
- **字数限制**: 活动标题文字限制为3-5个字
- **平台差异**: 通过platform参数区分美团(200)和点评(100)
- **图片生成**: 根据活动标题文字动态生成icon，底色橙色渐变，文字白色

### 3. 埋点边界
- **条件上报**: 仅在展示"神券价"时上报price_title字段
- **默认值**: 不展示神券价时price_title返回"-999"
- **兼容性**: 保持与现有埋点体系的兼容性

## 配置和开关控制

### 1. 实验开关
- **斗斛实验**: 通过ShelfDouHuFetcher控制神券价格展示实验
- **Matrix实验**: 通过MatrixExperimentFetcher控制相关功能

### 2. 业务开关
- **价格治理开关**: configUtils.isPriceOptimize()控制价格优化功能
- **Lion配置**: 各种业务功能的开关控制

### 3. 场景控制
- **sceneCode**: 通过场景码控制不同场景的展示逻辑
- **platform**: 通过平台参数控制美团/点评差异化逻辑
