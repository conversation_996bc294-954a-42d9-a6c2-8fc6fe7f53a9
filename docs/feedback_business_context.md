# 业务上下文与领域知识反馈文档

## 1. 严重缺失：神券业务领域深度理解

### 1.1 神券的业务本质
从实际代码可以看出，神券不仅仅是一种优惠券，而是一个复杂的**金融产品体系**：

#### 1.1.1 神券的膨胀机制
```java
// 膨胀状态判断的核心逻辑
boolean canInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
boolean afterInflate = BooleanUtils.toBoolean(extraInfo.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));

// 膨胀后是否能形成更优算价组合
boolean canInflateAndCanInflateMore = MagicalMemberTagUtils.getCanInflateAndCanInflateMore(req.getContext(),extendDisplayInfo);
```

**业务含义**：
- **膨胀前**: 神券有基础优惠金额
- **膨胀中**: 用户可以选择膨胀，增加优惠金额
- **膨胀后**: 膨胀完成，优惠金额固定
- **算价优化**: 膨胀后需要重新计算是否为最优组合

#### 1.1.2 神券的展示策略
```java
// 普通神券商家：不可膨胀
"神券icon（无膨胀箭头）已减X，共省Y"

// 膨胀神券商家：已膨胀或不可继续膨胀  
"神券icon（有膨胀箭头）已减X，共省Y"

// 膨胀神券商家：可膨胀且膨胀后更优
"神券icon（有膨胀箭头）最高膨至X|膨胀"
```

**业务逻辑**：
- 展示策略完全依赖于**服务端算价结果**
- 客户端不做复杂的业务判断，只做展示逻辑
- 膨胀引导基于**实时算价**，而非静态配置

### 1.2 神券与其他优惠的关系

#### 1.2.1 优惠组合优先级
```java
// 从PriceUtils.getUserHasPromoPrice()可以看出优先级
1. 商家会员卡优惠 (MerchantMemberPromo)
2. 折扣卡、玩乐卡优惠 (CardPromo) 
3. 立减优惠 (DirectPromo)
```

#### 1.2.2 神券在优惠体系中的位置
```java
// 神券是优惠组合的一部分，不是独立的优惠类型
ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(param.getProductM(), cardM);
boolean hasMagicalCoupon = DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM);
```

**关键理解**：
- 神券不改变优惠计算逻辑
- 神券只影响**展示方式**（前缀 + 标签）
- 神券的存在通过`PromotionExplanatoryTagEnum`标识

## 2. 严重缺失：货架展示业务规则

### 2.1 货架类型与展示差异

#### 2.1.1 单列 vs 双列货架
```java
public static boolean isDoubleColumnShelf(ActivityCxt context) {
    int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
    return doubleColumnShelf == 1;
}
```

**业务规则**：
- **单列货架**: 支持神券价格前缀展示
- **双列货架**: 不支持神券价格前缀（空间限制）
- **标签展示**: 双列货架的神券标签文案更简洁

#### 2.1.2 老货架 vs 统一货架
```java
// 老货架VP
ItemSalePricePrefixVP -> DefaultSalePricePrefixOpt

// 统一货架VP  
UnifiedShelfSalePricePrefixVP -> DefaultItemSalePricePrefixOpt
```

**技术含义**：
- 两套货架系统并行存在
- 需要保证功能一致性
- 通过共享静态方法实现逻辑复用

### 2.2 商品类型与展示规则

#### 2.2.1 次卡商品的特殊性
从代码中可以看出，次卡商品有特殊的处理逻辑：
```java
// TimesCardSalePricePrefixOpt 和 TimesDealSalePricePrefixOpt
// 专门处理次卡商品的价格前缀
```

**业务含义**：
- 次卡商品有独特的价格展示逻辑
- 可能不参与神券价格体系
- 需要专门的VP Option处理

#### 2.2.2 商品属性对展示的影响
```java
// 从测试代码可以看出多种边界条件
- 实验命中控制
- 客户端类型控制  
- 城市黑名单控制
- 货架版本控制
- 商品类型控制
```

## 3. 严重缺失：平台差异化业务逻辑

### 3.1 美团 vs 点评的差异

#### 3.1.1 客户端类型控制
```java
private List<Integer> clientType; // 200美团app
```

#### 3.1.2 城市维度控制
```java
private List<Integer> blackDpCityIds; // 黑名单城市
```

**业务含义**：
- 不同平台的用户体验策略不同
- 需要支持城市级别的功能开关
- 客户端版本兼容性要求

### 3.2 版本兼容性策略

#### 3.2.1 货架版本控制
```java
private int lowestShelfVersion; // 神会员最低货架版本
```

#### 3.2.2 渐进式功能发布
```java
// 通过版本号控制功能的渐进式发布
if (!MagicMemberUtil.satisfiedShelfVersion(shelfVersion, lowestShelfVersion)) {
    return null;
}
```

## 4. 严重缺失：实验与配置管理策略

### 4.1 多层次实验控制

#### 4.1.1 斗斛实验系统
```java
List<String> expSkWhitelist = magicalMemberConfig.getExpSkWhitelist();
List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
if (DouHuUtils.hitAnySk(douHuMList, expSkWhitelist)) {
    return true;
}
```

#### 4.1.2 配置系统集成
```java
@ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.magical.member.config1", defaultValue = "{}")
public static MagicalMemberConfig magicalMemberConfig;
```

**技术架构**：
- **斗斛实验**: 用于A/B测试和灰度发布
- **配置中心**: 用于运行时配置管理
- **多维度控制**: 实验 + 配置 + 业务逻辑

### 4.2 风险控制机制

#### 4.2.1 城市级别开关
```java
// 支持城市级别的紧急下线
if (magicalMemberConfig.getBlackDpCityIds().contains(-1L)) {
    return true; // 全量下线
}
```

#### 4.2.2 客户端兼容性保护
```java
// 通过客户端类型和版本号保护老版本用户
if (!MagicalMemberTagUtils.hitClientType(context)) {
    return null;
}
```

## 5. 严重缺失：数据来源与服务依赖

### 5.1 神券数据的来源链路

#### 5.1.1 扩展展示信息
```java
Map<String, String> extendDisplayInfo = DzPromoUtils.getExtendDisplayInfo(promoPriceM);
String magicalMemberCouponLabel = extendDisplayInfo.get(MAGICAL_MEMBER_COUPON_LABEL);
```

#### 5.1.2 膨胀判断服务
```java
MagicalMemberTagTextDTO magicalMemberTagTextDTO = GsonUtils.fromJson(magicalMemberCouponLabel, MagicalMemberTagTextDTO.class);
return Objects.equals(magicalMemberTagTextDTO.getShowType(), MagicalMemberTagShowTypeEnum.INFLATED_IS_BETTER_MMC.getValue());
```

**关键理解**：
- 膨胀判断完全依赖**服务端返回的数据**
- 客户端不做复杂的算价逻辑
- 展示策略基于服务端的**实时计算结果**

### 5.2 服务依赖关系

#### 5.2.1 优惠计算服务
- `PriceUtils.getUserHasPromoPrice()` - 获取最优优惠组合
- `DzPromoUtils.promoCombinationWithMagicalMemberCoupon()` - 判断是否包含神券

#### 5.2.2 配置服务
- 斗斛实验服务 - 提供A/B测试配置
- 配置中心 - 提供运行时配置

#### 5.2.3 用户状态服务
- `CardFetcher` - 获取用户持卡状态
- 城市定位服务 - 获取用户城市信息

## 6. 业务规则总结

### 6.1 神券展示的核心原则

1. **服务端驱动**: 所有复杂判断由服务端完成
2. **客户端展示**: 客户端只负责展示逻辑
3. **配置可控**: 通过多维度配置控制功能开关
4. **渐进发布**: 支持实验和灰度发布

### 6.2 展示策略的业务逻辑

1. **前缀展示**: 基于优惠组合判断是否展示"神券价"前缀
2. **标签展示**: 基于膨胀状态展示不同的标签文案
3. **埋点上报**: 基于实际展示内容进行埋点
4. **兼容性**: 考虑不同平台、版本、场景的差异

### 6.3 风险控制的业务要求

1. **可回滚**: 支持快速下线功能
2. **可控制**: 支持城市、客户端、版本等维度控制
3. **可监控**: 通过埋点监控功能效果
4. **可实验**: 支持A/B测试和灰度发布

## 7. 对技术方案设计的指导

### 7.1 业务理解优先
- 深入理解神券的业务模型
- 理解货架展示的业务规则
- 理解平台差异化的业务需求

### 7.2 服务端能力依赖
- 不要在客户端重复服务端的复杂逻辑
- 充分利用服务端提供的判断结果
- 保持客户端逻辑的简洁性

### 7.3 配置驱动设计
- 通过配置控制业务行为
- 支持多维度的功能开关
- 考虑风险控制和回滚机制

### 7.4 渐进式发布
- 支持实验和灰度发布
- 考虑版本兼容性
- 建立完善的监控体系

这些业务上下文和领域知识是设计技术方案的重要基础，缺少这些理解很容易导致方案偏离业务实际需求。
