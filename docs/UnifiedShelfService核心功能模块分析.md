# UnifiedShelfService接口核心功能模块分析

## 概述

UnifiedShelfService是美团点评统一货架服务的核心接口，通过`activity_unified_shelf`活动码配置，提供POI货架统一服务，包括查询货架导航及商品列表等功能。该服务基于PMF（Platform Management Framework）框架构建，采用能力组合和扩展点模式实现高度可配置的货架系统。

## 本次需求相关功能

根据需求文档分析，本次主要涉及以下功能模块：

### 1. 团购货架价格体系分层
- **神券价格展示**：区分神券价和普通到手价
- **价格计算逻辑**：最优算价组合包含神券时展示"神券价"文案
- **覆盖范围**：单列货架（不包含双列货架），包含老货架和POI一致性货架

### 2. 神券标签展示优化
- **膨胀信息展示**：神券标签展示膨胀和已减信息
- **膨胀引导**：膨后最优引导膨胀，膨后非最优展示已减
- **标签样式**：区分普通神券商家和膨胀神券商家的展示逻辑

### 3. SPU货架活动标题传参
- **活动标题文字**：运营配置的活动主标题文字
- **活动标题icon**：根据活动标题生成的icon图片
- **数据传递**：为适配SPU货架增加营销活动氛围数据

### 4. 特团氛围下线
- **商品标签处理**：商详页团购货架的商品标签优化
- **快筛tab处理**：相关筛选标签的处理逻辑

### 5. 埋点增强
- **价格名称字段**：增加price_title字段，展示"神券价"时上报
- **Ocean埋点**：完善货架商品的埋点信息

## 核心功能模块

### 1. 数据获取层（Model层）

#### 1.1 实验配置获取
- **ShelfDouHuFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher`)
  - 功能：根据配置查询斗斛实验，构造 `List<DouHuM>`
  - 作用：为货架提供A/B测试实验配置支持
  - **需求相关**：支持神券价格展示、神券标签样式等实验控制

- **MatrixExperimentFetcher**
  - 功能：根据配置查询matrix实验，构造 `List<MatrixExperimentM>`
  - 限制：仅支持美团平台

#### 1.2 商品数据获取
- **DealQueryFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher`)
  - 功能：团单召回能力，召回商品并构造 `Map<String, ProductGroupM>`
  - 依赖：FilterFirstFetcher、PreHandlerContextAbility
  - **需求相关**：召回的商品需要包含神券价格信息和活动标题数据

- **ProductPaddingFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpadding.ProductPaddingFetcher`)
  - 功能：商品填充能力，支持多种商品类型填充
  - 依赖：DealQueryFetcher、ProductPrePaddingFetcher、ShopProductPaddingFetcher、ProductParallelQueryPostHandler
  - **需求相关**：填充商品的价格信息、神券信息、活动信息

- **ProductParallelQueryFetcher**
  - 功能：与导航并行召回商品列表，构造 `Map<String, ProductGroupM>`
  - 依赖：ShelfDouHuFetcher、MatrixExperimentFetcher、PreHandlerContextAbility

- **ShopProductPaddingFetcher**
  - 功能：门店全量在线商品召回并填充能力
  - 依赖：ShelfDouHuFetcher、PreHandlerContextAbility

#### 1.3 筛选数据获取
- **FilterFirstFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher`)
  - 功能：筛选器前置召回能力，主要来源于外部数据源，构造 `Map<String, FilterM>`
  - 依赖：ShelfDouHuFetcher、MatrixExperimentFetcher、PreHandlerContextAbility
  - **需求相关**：处理特团氛围下线相关的筛选逻辑

#### 1.4 其他数据获取
- **ProductActivitiesFetcher**
  - 功能：查询商品活动列表数据，包括活动类型、图片等，构造 `List<ProductActivityM>`
  - **需求相关**：获取SPU货架所需的活动标题文字和活动标题icon数据

- **CardFetcher**
  - 功能：查询卡数据，包括持卡状态等，构造 `CardM`
  - **需求相关**：神券价格计算需要用户持卡状态信息

### 2. 数据组装层（Model层）

#### 2.1 主数据组装
- **ShelfMainDataAssembler** (`com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler`)
  - 功能：组装货架内部数据模型，构造 `ShelfGroupM`
  - 依赖：ProductPaddingFetcher、FilterFirstFetcher、DealQueryFetcher、FilterLastFetcher、ShelfDouHuFetcher、ContextHandlerAbility

### 3. 视图构建层（VO层）

#### 3.1 货架响应组装
- **UnifiedShelfResponseAssembler** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.UnifiedShelfResponseAssembler`)
  - 功能：货架生成能力，最终响应组装
  - 依赖：UnifiedShelfFilterBuilder、UnifiedShelfOceanBuilder、UnifiedProductAreaBuilder、UnifiedShelfMainTitleBuilder
  - **需求相关**：组装包含神券价格、活动标题等信息的最终响应

#### 3.2 具体组件构建
- **UnifiedProductAreaBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder`)
  - 功能：商品区构造能力
  - 依赖：ShelfMainDataAssembler、CardFetcher、ProductActivitiesFetcher
  - **需求相关**：构造包含神券价格、神券标签、活动标题的商品区域

- **UnifiedShelfFilterBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder`)
  - 功能：筛选栏构造能力（前置），构造 `Map<String, ShelfFilterVO>`
  - 依赖：ShelfMainDataAssembler
  - **需求相关**：处理特团氛围下线后的筛选栏展示

- **UnifiedShelfMainTitleBuilder**
  - 功能：标题组件构造能力，构造 `ShelfMainTitleVO`
  - 依赖：ShelfMainDataAssembler

- **UnifiedShelfOceanBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.UnifiedShelfOceanBuilder`)
  - 功能：Ocean 打点构造
  - 依赖：ShelfMainDataAssembler
  - **需求相关**：增加price_title字段的埋点信息

### 4. 上下文处理层

#### 4.1 上下文预处理
- **PreHandlerContextAbility** (`com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility`)
  - 功能：上下文预处理能力，提供可选的上下文填充
  - 依赖：ShelfDouHuFetcher

- **ContextHandlerAbility**
  - 功能：异步处理上下文
  - 依赖：ShelfDouHuFetcher

- **ProductParallelQueryPostHandler**
  - 功能：导航和商品并行召回后置处理能力点
  - 依赖：FilterFirstFetcher、ProductParallelQueryFetcher

## 扩展点体系

### 1. 变化点（VP - Variation Point）

#### 1.1 货架级别变化点
- **UnifiedShelfShowTypeVP**：货架展示样式控制
  - **需求相关**：控制单列/双列货架的展示样式
- **UnifiedShelfMainTitleVP**：主标题变化点

#### 1.2 筛选级别变化点
- **UnifiedShelfFilterTitleVP**：筛选按钮名字
  - **需求相关**：处理特团氛围下线后的筛选按钮文案
- **UnifiedShelfFilterBtnLabsVP**：筛选Ocean labs
- **UnifiedShelfFilterShowTypeVP**：筛选展示类型
- **UnifiedShelfExtraVP**：筛选扩展信息

#### 1.3 商品级别变化点
- **UnifiedShelfItemTitleVP**：商品标题
  - **需求相关**：SPU货架活动标题的展示逻辑
- **UnifiedShelfItemWarmUpVP**：预热信息
- **ProductAreaPreBuildHandlerVP**：商品区构造前置处理

#### 1.4 业务逻辑变化点
- **ProductAnchorVP**：货架商品召回-商品锚定路由扩展点
- **ShelfGroupAssemblerVP**：货架商品区分组扩展点
- **FilterValidateVP**：货架筛选栏校验器扩展点
- **FilterSelectedIdVP**：货架筛选栏筛选器扩展点

### 2. 预处理变化点
- **PreAsyncHandlerVP**：并行预处理变化点
- **PreSyncHandlerVP**：串行预处理变化点
- **InterceptHandlerVP**：拦截预处理变化点

## 核心流程

1. **请求接收**：UnifiedShelfServiceImpl接收请求
2. **活动执行**：通过UnifiedShelfExecutor执行活动引擎
3. **上下文预处理**：PreHandlerContextAbility处理上下文信息
4. **数据获取**：并行获取实验配置、商品数据、筛选数据等
5. **数据组装**：ShelfMainDataAssembler组装主数据模型
6. **视图构建**：各Builder构建具体的视图组件
7. **响应组装**：UnifiedShelfResponseAssembler组装最终响应

## 技术特点

1. **高度可配置**：通过配置化参数控制各个能力的行为
2. **扩展性强**：基于变化点模式，支持业务定制化需求
3. **并行处理**：支持数据获取和处理的并行化
4. **容错机制**：内置容错和降级处理
5. **监控完善**：集成Cat监控和日志记录

## 本次需求涉及的关键代码路径

### 1. 神券价格处理链路

#### 1.1 价格计算核心类
- **PriceUtils** (`com.sankuai.dzviewscene.product.utils.PriceUtils`)
  - `getUserHasPromoPrice()`: 获取用户最优优惠价格，包含神券价格逻辑
  - 处理商家会员卡、折扣卡、玩乐卡等多种优惠组合

- **PriceDisplayUtils** (`com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils`)
  - `getSalePrice()`: 计算到手价，支持神券价格
  - `batchSetSalePrice()`: 批量设置团单到手价

#### 1.2 神券标签处理
- **MagicalMemberPromoTagStrategy** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo.MagicalMemberPromoTagStrategy`)
  - `buildTag()`: 构建神券优惠标签
  - 处理膨胀信息展示逻辑
  - 区分普通神券和膨胀神券的展示样式

#### 1.3 价格底部标签构建
- **ItemPriceBottomTagsVP**: 商品价格底部标签变化点
  - 多个实现类处理不同场景的价格标签
  - 支持神券标签、优惠标签、比价标签等

### 2. SPU货架活动标题处理链路

#### 2.1 活动数据获取
- **ProductActivitiesFetcher**: 查询商品活动列表数据
  - 获取活动类型、活动标题文字、活动标题icon等信息
  - 构造 `List<ProductActivityM>` 数据结构

#### 2.2 活动标题展示
- **UnifiedShelfItemTitleVP**: 商品标题变化点
  - 多个Option处理不同类型的标题展示
  - `UnifiedShelfItemTrySpuNameTitleOpt`: 支持SPU名称标题
  - 处理活动标题文字的展示逻辑

### 3. 特团氛围下线处理链路

#### 3.1 筛选标签处理
- **UnifiedShelfFilterTitleVP**: 筛选按钮名字变化点
  - `UnifiedShelfMultiActivityTitleOpt`: 多活动筛选图片按钮
  - 处理特价团购tab的展示逻辑

#### 3.2 商品标签优化
- **ProductActivityTagsVP**: 商品活动标签变化点
  - 多个Option处理不同场景的活动标签
  - 支持特团氛围的下线处理

### 4. 埋点增强处理链路

#### 4.1 Ocean埋点构建
- **UnifiedShelfOceanBuilder**: Ocean打点构造能力
- **UnifiedShelfItemOceanLabsVP**: 商品埋点变化点
  - `UnifiedShelfItemCommonOceanLabsOpt`: 通用商品埋点
  - 支持price_title字段的埋点上报

#### 4.2 埋点数据填充
- **UnifiedShelfCommonOcean** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils.UnifiedShelfCommonOcean`)
  - `paddingCommonOcean()`: 填充通用埋点信息
  - 处理神券价格相关的埋点字段

### 5. 边界情况识别

#### 5.1 价格展示边界
- **次卡商品**: 不包含在神券价格体系中，价格表达保持现状
- **双列货架**: 不包含在神券价格分层范围内
- **倒挂场景**: 卡优惠倒挂时的价格处理逻辑

#### 5.2 活动标题边界
- **字数限制**: 活动标题文字为3-5个字
- **平台差异**: 美团和点评平台的差异化处理
- **图片生成**: 活动标题icon的动态生成逻辑

#### 5.3 埋点边界
- **条件上报**: 仅在展示"神券价"时上报price_title字段
- **默认值**: 不展示神券价时返回"-999"
- **兼容性**: 保证与现有埋点体系的兼容性

该架构设计充分体现了美团点评在大规模电商场景下对系统可扩展性、可维护性和性能的要求。
