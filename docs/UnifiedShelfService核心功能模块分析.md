# UnifiedShelfService接口核心功能模块分析

## 概述

UnifiedShelfService是美团点评统一货架服务的核心接口，通过`activity_unified_shelf`活动码配置，提供POI货架统一服务，包括查询货架导航及商品列表等功能。该服务基于PMF（Platform Management Framework）框架构建，采用能力组合和扩展点模式实现高度可配置的货架系统。

## 核心功能模块

### 1. 数据获取层（Model层）

#### 1.1 实验配置获取
- **ShelfDouHuFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher`)
  - 功能：根据配置查询斗斛实验，构造 `List<DouHuM>`
  - 作用：为货架提供A/B测试实验配置支持

- **MatrixExperimentFetcher** 
  - 功能：根据配置查询matrix实验，构造 `List<MatrixExperimentM>`
  - 限制：仅支持美团平台

#### 1.2 商品数据获取
- **DealQueryFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher`)
  - 功能：团单召回能力，召回商品并构造 `Map<String, ProductGroupM>`
  - 依赖：FilterFirstFetcher、PreHandlerContextAbility

- **ProductPaddingFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpadding.ProductPaddingFetcher`)
  - 功能：商品填充能力，支持多种商品类型填充
  - 依赖：DealQueryFetcher、ProductPrePaddingFetcher、ShopProductPaddingFetcher、ProductParallelQueryPostHandler

- **ProductParallelQueryFetcher**
  - 功能：与导航并行召回商品列表，构造 `Map<String, ProductGroupM>`
  - 依赖：ShelfDouHuFetcher、MatrixExperimentFetcher、PreHandlerContextAbility

- **ShopProductPaddingFetcher**
  - 功能：门店全量在线商品召回并填充能力
  - 依赖：ShelfDouHuFetcher、PreHandlerContextAbility

#### 1.3 筛选数据获取
- **FilterFirstFetcher** (`com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher`)
  - 功能：筛选器前置召回能力，主要来源于外部数据源，构造 `Map<String, FilterM>`
  - 依赖：ShelfDouHuFetcher、MatrixExperimentFetcher、PreHandlerContextAbility

#### 1.4 其他数据获取
- **ProductActivitiesFetcher**
  - 功能：查询商品活动列表数据，包括活动类型、图片等，构造 `List<ProductActivityM>`

- **CardFetcher**
  - 功能：查询卡数据，包括持卡状态等，构造 `CardM`

### 2. 数据组装层（Model层）

#### 2.1 主数据组装
- **ShelfMainDataAssembler** (`com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler`)
  - 功能：组装货架内部数据模型，构造 `ShelfGroupM`
  - 依赖：ProductPaddingFetcher、FilterFirstFetcher、DealQueryFetcher、FilterLastFetcher、ShelfDouHuFetcher、ContextHandlerAbility

### 3. 视图构建层（VO层）

#### 3.1 货架响应组装
- **UnifiedShelfResponseAssembler** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.UnifiedShelfResponseAssembler`)
  - 功能：货架生成能力，最终响应组装
  - 依赖：UnifiedShelfFilterBuilder、UnifiedShelfOceanBuilder、UnifiedProductAreaBuilder、UnifiedShelfMainTitleBuilder

#### 3.2 具体组件构建
- **UnifiedProductAreaBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder`)
  - 功能：商品区构造能力
  - 依赖：ShelfMainDataAssembler、CardFetcher、ProductActivitiesFetcher

- **UnifiedShelfFilterBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder`)
  - 功能：筛选栏构造能力（前置），构造 `Map<String, ShelfFilterVO>`
  - 依赖：ShelfMainDataAssembler

- **UnifiedShelfMainTitleBuilder**
  - 功能：标题组件构造能力，构造 `ShelfMainTitleVO`
  - 依赖：ShelfMainDataAssembler

- **UnifiedShelfOceanBuilder** (`com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.UnifiedShelfOceanBuilder`)
  - 功能：Ocean 打点构造
  - 依赖：ShelfMainDataAssembler

### 4. 上下文处理层

#### 4.1 上下文预处理
- **PreHandlerContextAbility** (`com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility`)
  - 功能：上下文预处理能力，提供可选的上下文填充
  - 依赖：ShelfDouHuFetcher

- **ContextHandlerAbility**
  - 功能：异步处理上下文
  - 依赖：ShelfDouHuFetcher

- **ProductParallelQueryPostHandler**
  - 功能：导航和商品并行召回后置处理能力点
  - 依赖：FilterFirstFetcher、ProductParallelQueryFetcher

## 扩展点体系

### 1. 变化点（VP - Variation Point）

#### 1.1 货架级别变化点
- **UnifiedShelfShowTypeVP**：货架展示样式控制
- **UnifiedShelfMainTitleVP**：主标题变化点

#### 1.2 筛选级别变化点
- **UnifiedShelfFilterTitleVP**：筛选按钮名字
- **UnifiedShelfFilterBtnLabsVP**：筛选Ocean labs
- **UnifiedShelfFilterShowTypeVP**：筛选展示类型
- **UnifiedShelfExtraVP**：筛选扩展信息

#### 1.3 商品级别变化点
- **UnifiedShelfItemTitleVP**：商品标题
- **UnifiedShelfItemWarmUpVP**：预热信息
- **ProductAreaPreBuildHandlerVP**：商品区构造前置处理

#### 1.4 业务逻辑变化点
- **ProductAnchorVP**：货架商品召回-商品锚定路由扩展点
- **ShelfGroupAssemblerVP**：货架商品区分组扩展点
- **FilterValidateVP**：货架筛选栏校验器扩展点
- **FilterSelectedIdVP**：货架筛选栏筛选器扩展点

### 2. 预处理变化点
- **PreAsyncHandlerVP**：并行预处理变化点
- **PreSyncHandlerVP**：串行预处理变化点
- **InterceptHandlerVP**：拦截预处理变化点

## 核心流程

1. **请求接收**：UnifiedShelfServiceImpl接收请求
2. **活动执行**：通过UnifiedShelfExecutor执行活动引擎
3. **上下文预处理**：PreHandlerContextAbility处理上下文信息
4. **数据获取**：并行获取实验配置、商品数据、筛选数据等
5. **数据组装**：ShelfMainDataAssembler组装主数据模型
6. **视图构建**：各Builder构建具体的视图组件
7. **响应组装**：UnifiedShelfResponseAssembler组装最终响应

## 技术特点

1. **高度可配置**：通过配置化参数控制各个能力的行为
2. **扩展性强**：基于变化点模式，支持业务定制化需求
3. **并行处理**：支持数据获取和处理的并行化
4. **容错机制**：内置容错和降级处理
5. **监控完善**：集成Cat监控和日志记录

该架构设计充分体现了美团点评在大规模电商场景下对系统可扩展性、可维护性和性能的要求。
