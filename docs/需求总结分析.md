# 团购货架神券价格体系需求总结分析

## 需求背景

本次需求旨在全链路树立神券价格心智，主要包括：
1. 货架和团详价格区分"神券价"和团购价
2. 货架神券标签透传膨胀和已减信息
3. SPU货架营销活动氛围增强
4. 特团氛围下线优化
5. 埋点信息完善

## 核心技术实现

### 1. 神券价格体系分层

#### 技术方案
- **价格计算**: 通过PriceUtils.getUserHasPromoPrice()获取最优优惠组合
- **神券判断**: 使用DzPromoUtils.promoCombinationWithMagicalMemberCoupon()判断是否包含神券
- **价格展示**: 在PriceDisplayUtils.getSalePrice()中实现神券价格逻辑

#### 展示逻辑
```
if (包含神券) {
    展示: "神券价 ¥XX"
} else {
    展示: "¥XX"
}
```

#### 覆盖范围
- ✅ 单列货架（老货架和POI一致性货架）
- ✅ 货架二级落地页
- ❌ 双列货架
- ❌ 次卡商品

### 2. 神券标签展示优化

#### 技术方案
- **标签构建**: MagicalMemberPromoTagStrategy.buildTag()
- **样式控制**: 通过实验配置选择新旧样式
- **文案生成**: buildCommonTagText()根据膨胀状态生成不同文案

#### 展示规则

##### 普通神券商家
- **不可膨胀**: 
  - 单列: "神券icon（无膨胀箭头）已减X，共省Y"
  - 双列: "神券icon（无膨胀箭头）共省Y"

##### 膨胀神券商家
- **不可膨胀（含已膨胀）**:
  - 单列: "神券icon（有膨胀箭头）已减X，共省Y"
  - 双列: "神券icon（有膨胀箭头）共省Y"
  
- **可膨胀且膨胀后更优**:
  - 单列: "神券icon（有膨胀箭头）最高膨至X|膨胀"

### 3. SPU货架活动标题传参

#### 技术方案
- **数据获取**: ProductActivitiesFetcher查询活动列表数据
- **标题展示**: UnifiedShelfItemTitleVP处理标题展示逻辑
- **数据结构**: 增加活动标题文字(text)和活动标题icon(图片)字段

#### 数据结构
```json
{
  "activityTitleText": "惊喜买赠",
  "activityTitleIcon": "https://xxx.jpg"
}
```

#### 图片规范
- **底色**: 固定橙色渐变
- **文字**: 固定白色
- **字数**: 3-5个字
- **尺寸**: 高度固定，长度根据字数变化

### 4. 特团氛围下线

#### 技术方案
- **筛选处理**: UnifiedShelfFilterTitleVP.UnifiedShelfMultiActivityTitleOpt
- **标签处理**: ProductActivityTagsVP相关实现类
- **展示控制**: 通过配置控制特价团购相关氛围的展示

#### 处理逻辑
- 特价团购tab在有其他活动时不展示图片，仅展示文案
- 其他活动正常展示图片
- 商品标签优化处理

### 5. 埋点增强

#### 技术方案
- **埋点构建**: UnifiedShelfOceanBuilder和相关VP
- **字段添加**: 增加price_title字段
- **条件上报**: 仅在展示"神券价"时上报

#### 埋点逻辑
```javascript
if (展示神券价) {
    price_title = "神券价"
} else {
    price_title = "-999"
}
```

## 关键代码路径

### 1. 核心调用链
```
UnifiedShelfServiceImpl
  ↓
UnifiedShelfExecutor
  ↓
UnifiedShelfActivityEngine
  ↓
各种Fetcher和Builder
  ↓
VP扩展点处理
  ↓
最终响应组装
```

### 2. 价格处理链
```
DealQueryFetcher → ProductPaddingFetcher → PriceUtils → PriceDisplayUtils
```

### 3. 标签处理链
```
UnifiedProductAreaBuilder → ItemPriceBottomTagsVP → MagicalMemberPromoTagStrategy
```

### 4. 埋点处理链
```
UnifiedShelfOceanBuilder → UnifiedShelfItemOceanLabsVP → UnifiedShelfCommonOcean
```

## 边界情况处理

### 1. 技术边界
- **次卡商品**: 通过ProductTypeEnum.TIME_CARD判断，不参与神券价格体系
- **双列货架**: 通过showType控制，不展示神券价格分层
- **倒挂场景**: CardPromoUtils.isCardDaoGuaPromo()处理价格倒挂

### 2. 业务边界
- **平台差异**: 通过platform参数区分美团(200)和点评(100)
- **实验控制**: 通过斗斛实验控制功能开关
- **场景控制**: 通过sceneCode控制不同场景逻辑

### 3. 数据边界
- **活动标题字数**: 限制3-5个字
- **埋点默认值**: price_title默认"-999"
- **图片生成**: 动态生成活动标题icon

## 风险点分析

### 1. 性能风险
- **价格计算复杂度**: 多种优惠组合的计算可能影响性能
- **并发处理**: 大量商品的价格和标签计算需要优化

### 2. 兼容性风险
- **老版本兼容**: 需要保证老版本客户端的兼容性
- **埋点兼容**: 新增埋点字段不能影响现有埋点体系

### 3. 业务风险
- **价格准确性**: 神券价格计算的准确性至关重要
- **标签一致性**: 不同场景下标签展示的一致性

## 测试建议

### 1. 功能测试
- 神券价格计算准确性测试
- 不同优惠组合的价格展示测试
- 神券标签样式和文案测试
- SPU货架活动标题展示测试

### 2. 边界测试
- 次卡商品价格展示测试
- 双列货架价格展示测试
- 价格倒挂场景测试
- 活动标题字数边界测试

### 3. 性能测试
- 大量商品价格计算性能测试
- 并发场景下的响应时间测试
- 埋点上报性能测试

### 4. 兼容性测试
- 不同版本客户端兼容性测试
- 不同平台（美团/点评）差异化测试
- 实验开关控制测试

## 上线建议

### 1. 分阶段上线
1. **第一阶段**: 神券价格体系分层
2. **第二阶段**: 神券标签展示优化
3. **第三阶段**: SPU货架活动标题和特团氛围下线
4. **第四阶段**: 埋点增强

### 2. 灰度策略
- 通过斗斛实验控制灰度比例
- 优先在低风险场景验证
- 逐步扩大覆盖范围

### 3. 监控告警
- 价格计算异常监控
- 标签展示异常监控
- 埋点上报异常监控
- 性能指标监控
