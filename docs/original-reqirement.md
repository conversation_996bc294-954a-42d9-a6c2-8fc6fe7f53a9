根据您的需求，以下是文档中所有与“团购货架”相关的章节和段落内容整理：

---

## 4.2 商详页

### 3）团购货架

- 改动点1：货架价格体系分层，区分神券价和普通到手价。

  - 展示逻辑：当该商品的最优算价组合包含神券时，到手价前【神券价】文案，否则不做说明。
  - 覆盖范围：
    - 单列货架（不包含双列货架），包含老货架和POI一致性货架，包含货架二级落地页。
    - 不包含次卡商品，次卡价格表达保持现状。
  - 示意图：  
    ![单列货架示意图](https://km.sankuai.com/api/file/cdn/2703966372/150654043409?contentType=1&isNewContent=false)

- 改动点2：货架神券标签展示膨胀和已减信息，膨后最优引导膨胀，膨后非最优展示已减。

  - 当该商品的最优算价组合包含神券时，优惠标签展示膨胀和已减信息：

    - 普通神券商家
      - 神券不可膨胀
        - 单列货架优惠标签：  
          ![神券icon（无膨胀箭头）已减X，共省Y](https://km.sankuai.com/api/file/cdn/2703966372/150572186035?contentType=1&isNewContent=false)
          - 神券icon（无膨胀箭头）
          - 【已减X】，X=该券的当前金额
          - 【共省Y】，Y=当前商品的全部优惠金额
          - 点击引导箭头：点击整个标签唤起优惠明细浮层
        - 双列货架优惠标签：  
          ![神券icon（无膨胀箭头）共省Y](https://km.sankuai.com/api/file/cdn/2703966372/150576312920?contentType=1&isNewContent=false)
          - 神券icon（无膨胀箭头）
          - 【共省Y】，Y=当前商品的全部优惠金额

    - 膨胀神券商家
      - 神券不可膨胀（含已膨胀场景）
        - 单列货架优惠标签：  
          ![神券icon（有膨胀箭头）已减X，共省Y](https://km.sankuai.com/api/file/cdn/2703966372/150575685278?contentType=1&isNewContent=false)
          - 神券icon（有膨胀箭头）
          - 【已减X】，X=该券当前金额
          - 【共省Y】，Y=当前商品的全部优惠金额
          - 点击引导箭头：点击整个标签唤起优惠明细浮层
        - 双列货架优惠标签：  
          ![神券icon（有膨胀箭头）共省Y](https://km.sankuai.com/api/file/cdn/2703966372/150569481711?contentType=1&isNewContent=false)
          - 神券icon（有膨胀箭头）
          - 【共省Y】，Y=当前商品的全部优惠金额

      - 神券还可膨胀，但膨胀后不能形成更优算价组合
        - 单列货架优惠标签同上。

      - 神券还可膨胀，且膨胀后能够形成更优算价组合
        - 单列货架优惠标签：  
          ![神券icon（有膨胀箭头）最高膨至X|膨胀](https://km.sankuai.com/api/file/cdn/2703966372/154496379011?contentType=1&isNewContent=false)
          - 神券icon（有膨胀箭头）
          - 【最高膨至X|膨胀】，X=该行业最高膨胀金额
          - 点击引导箭头：点击整个标签唤起优惠明细浮层

- 双列货架示意图：  
  ![双列货架示意图](https://km.sankuai.com/api/file/cdn/2703966372/150566947982?contentType=1&isNewContent=false)

---

## 三、需求概述

- 全链路树立神券价格心智
  - 货架和团详价格区分“神券价”和团购价
    - 产品模块：团购货架、团详页
    - 团队：泛交易
    - 示意图：  
      ![货架、团详价格区分“神券价”和团购价](https://km.sankuai.com/api/file/cdn/2703966372/151387390273?contentType=1&isNewContent=false)

- 主路径强化神券买膨领引导
  - 货架神券标签透传膨胀和已减信息
    - 产品模块：团购货架
    - 团队：泛交易

---

## 四、需求详述

### 五、货架传参

- 概述：为适配SPU货架，货架营销活动氛围需增加以下两类数据：
  1. 当团单（deal）存在活动时，除当前的【头图活动标签】外，新增【活动标题文字】和【活动标题icon】传给货架展示侧

- 数据结构说明：
  1. 活动标题文字（text）：运营同学彩虹后台配置的活动主标题文字。例如：“惊喜买赠”
  2. 活动标题icon（图片）：根据运营同学彩虹后台配置的活动主标题文字，生成标题icon图片。底色固定橙色渐变、文字固定白色，尺寸参考视觉稿（因活动标题字数为3-5个字，长度存在变化，高度固定不变）。

- SPU货架示意图：  
  ![SPU货架示意](https://km.sankuai.com/api/file/cdn/2703966372/154686310383?contentType=1&isNewContent=false)
- 活动标题icon示例：  
  ![活动标题icon](https://km.sankuai.com/api/file/cdn/2703966372/154689970189?contentType=1&isNewContent=false)
- 视觉稿参考链接：https://ingee.meituan.com/#/artboard/1208851/default

---

## 四、需求详述

### 4.5 主路径下线特团氛围

- 覆盖页面-模块：

  | 页面           | 产品模块         | 示意图                                                                                 |
  |----------------|------------------|----------------------------------------------------------------------------------------|
  | 商详页-团购货架 | 商品标签+快筛tab | ![商详页-团购货架](https://km.sankuai.com/api/file/cdn/2703966372/151726686941?contentType=1&isNewContent=false) |

---

## 七、埋点

- 到综全量货架_单个商品mv、mc埋点
  - 增加一个价格名称字段，当C端展示“神券价”时上报price_title，否则返回“-999”。
  - 价格字段：price
  - 示意图：  
    ![埋点字段示意图](https://km.sankuai.com/api/file/cdn/2703966372/156500026113?contentType=1)
    ![埋点示意图](https://km.sankuai.com/api/file/cdn/2703966372/156503388304?contentType=1&isNewContent=false)

---

以上内容即为文档中所有与“团购货架”相关的章节及其详细描述、表格、图片和链接。